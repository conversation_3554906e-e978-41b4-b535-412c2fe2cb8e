## 异步编程

### Promise基础（12道）

# **116. [初级]** 什么是Promise？它解决了什么问题？

promise es6新增的用于处理异步请求的新方式。通过链式调用解决了之前的回调地狱问题。promise 状态一旦发生变化就不会再改变

## 深度分析与补充

**问题本质解读：** 这道题考察对Promise核心概念的理解和异步编程发展历程的认知，面试官想了解你是否理解Promise的设计初衷和实际价值。

**知识点系统梳理：**

**Promise的本质：**

- Promise是一个代表异步操作最终完成或失败的对象
- 它是一个容器，保存着某个未来才会结束的事件的结果
- Promise提供统一的API，各种异步操作都可以用同样的方法进行处理

**解决的核心问题：**

1. **回调地狱（Callback Hell）**：多层嵌套回调导致代码难以维护
2. **错误处理困难**：传统回调中错误处理分散且容易遗漏
3. **控制反转**：将回调函数的控制权交给第三方库，缺乏可靠性保证
4. **缺乏组合性**：难以组合多个异步操作

**实战应用举例：**

**通用JavaScript示例：**

```javascript
// ❌ 回调地狱示例
function getUserData(userId, callback) {
  getUser(userId, (err, user) => {
    if (err) {
      callback(err, null)
      return
    }
    getProfile(user.id, (err, profile) => {
      if (err) {
        callback(err, null)
        return
      }
      getPreferences(user.id, (err, preferences) => {
        if (err) {
          callback(err, null)
          return
        }
        callback(null, { user, profile, preferences })
      })
    })
  })
}

// ✅ Promise链式调用解决方案
function getUserData(userId) {
  return getUser(userId)
    .then(user => {
      return Promise.all([user, getProfile(user.id), getPreferences(user.id)])
    })
    .then(([user, profile, preferences]) => {
      return { user, profile, preferences }
    })
    .catch(error => {
      console.error('获取用户数据失败:', error)
      throw error
    })
}

// ✅ 现代async/await写法
async function getUserData(userId) {
  try {
    const user = await getUser(userId)
    const [profile, preferences] = await Promise.all([getProfile(user.id), getPreferences(user.id)])
    return { user, profile, preferences }
  } catch (error) {
    console.error('获取用户数据失败:', error)
    throw error
  }
}

// Promise基础创建和使用
const fetchData = () => {
  return new Promise((resolve, reject) => {
    // 模拟异步操作
    setTimeout(() => {
      const success = Math.random() > 0.5
      if (success) {
        resolve({ data: '请求成功', timestamp: Date.now() })
      } else {
        reject(new Error('网络请求失败'))
      }
    }, 1000)
  })
}

// 使用Promise
fetchData()
  .then(result => {
    console.log('成功:', result)
    return result.data
  })
  .then(data => {
    console.log('处理数据:', data)
  })
  .catch(error => {
    console.error('错误:', error.message)
  })
  .finally(() => {
    console.log('请求完成')
  })
```

**Promise的核心特性：**

1. **状态不可逆**：pending → fulfilled/rejected，状态一旦改变就不会再变
2. **链式调用**：每个then方法返回新的Promise，支持链式操作
3. **错误传播**：错误会沿着Promise链向下传播，直到被catch捕获
4. **值传递**：resolve的值会传递给下一个then的回调函数

**Vue 3框架应用示例：**

```vue
<template>
  <div class="user-profile">
    <div
      v-if="loading"
      class="loading"
    >
      加载中...
    </div>
    <div
      v-else-if="error"
      class="error"
    >
      {{ error }}
    </div>
    <div
      v-else-if="userData"
      class="profile"
    >
      <h2>{{ userData.user.name }}</h2>
      <p>文章数量: {{ userData.posts.length }}</p>
      <p>好友数量: {{ userData.friends.length }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const userData = ref(null)
const loading = ref(false)
const error = ref(null)

// 使用Promise解决Vue组件中的异步数据加载
const fetchUserData = async userId => {
  loading.value = true
  error.value = null

  try {
    // Promise链式调用获取用户完整信息
    const user = await getUserAPI(userId)
    const [posts, friends] = await Promise.all([
      getUserPostsAPI(user.id),
      getUserFriendsAPI(user.id),
    ])

    userData.value = { user, posts, friends }
  } catch (err) {
    error.value = '获取用户数据失败: ' + err.message
  } finally {
    loading.value = false
  }
}

// 组合式函数：封装Promise逻辑
const useUserData = userId => {
  const data = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const fetch = () => {
    return fetchUserData(userId)
      .then(result => {
        data.value = result
        return result
      })
      .catch(err => {
        error.value = err.message
        throw err
      })
  }

  return { data, loading, error, fetch }
}

onMounted(() => {
  fetchUserData(123)
})
</script>
```

**记忆要点总结：**

- Promise是异步编程的解决方案，解决回调地狱问题
- 三个状态：pending（等待）、fulfilled（成功）、rejected（失败）
- 核心方法：then、catch、finally
- 状态不可逆，一旦改变就不会再变
- 支持链式调用，提供更好的错误处理机制

# **117. [初级]** Promise有哪几种状态？

- pending 等待
- fulfilled 执行成功的原因
- rejected 执行拒绝的理由

## 深度分析与补充

**问题本质解读：** 这道题考察Promise状态机制的理解，面试官想了解你是否掌握Promise的核心工作原理和状态转换规则。

**技术错误纠正：**

- fulfilled应该是"已成功"状态，不是"执行成功的原因"
- rejected应该是"已拒绝"状态，不是"执行拒绝的理由"

**知识点系统梳理：**

**Promise的三种状态：**

1. **pending（等待中）**：初始状态，既不是成功，也不是失败状态
2. **fulfilled（已成功）**：操作成功完成
3. **rejected（已拒绝）**：操作失败

**状态转换规则：**

- pending → fulfilled：通过调用resolve()
- pending → rejected：通过调用reject()或抛出异常
- 状态一旦改变就不可逆，不能从fulfilled或rejected再变回pending
- fulfilled和rejected之间不能相互转换

**实战应用举例：**

```javascript
// 演示Promise状态变化
function createPromiseDemo() {
  console.log('1. 创建Promise，初始状态为pending')

  const promise = new Promise((resolve, reject) => {
    console.log('2. Promise执行器立即执行')

    setTimeout(() => {
      const random = Math.random()
      if (random > 0.5) {
        console.log('3. 调用resolve，状态变为fulfilled')
        resolve(`成功结果: ${random}`)
      } else {
        console.log('3. 调用reject，状态变为rejected')
        reject(new Error(`失败原因: ${random}`))
      }
    }, 1000)
  })

  // 检查Promise状态的方法（仅用于演示，实际开发中不推荐）
  console.log('Promise状态:', getPromiseState(promise))

  return promise
}

// 辅助函数：获取Promise状态（仅用于演示）
function getPromiseState(promise) {
  const t = {}
  return Promise.race([promise, t]).then(
    v => (v === t ? 'pending' : 'fulfilled'),
    () => 'rejected',
  )
}

// 状态转换示例
const demo1 = new Promise((resolve, reject) => {
  // 状态：pending
  console.log('当前状态: pending')

  setTimeout(() => {
    resolve('成功')
    // 状态：fulfilled
    // 下面的调用将被忽略，因为状态已经确定
    reject('失败') // 无效
    resolve('再次成功') // 无效
  }, 1000)
})

// 错误处理导致状态变为rejected
const demo2 = new Promise((resolve, reject) => {
  try {
    // 模拟可能出错的操作
    const result = JSON.parse('invalid json')
    resolve(result)
  } catch (error) {
    reject(error) // 状态变为rejected
  }
})

// 使用Promise.resolve和Promise.reject创建特定状态的Promise
const fulfilledPromise = Promise.resolve('直接成功')
const rejectedPromise = Promise.reject(new Error('直接失败'))

// 状态检测和处理
demo1
  .then(value => {
    console.log('Promise已fulfilled，值为:', value)
  })
  .catch(error => {
    console.log('Promise已rejected，错误为:', error)
  })
  .finally(() => {
    console.log('Promise已settled（已敲定，不管是fulfilled还是rejected）')
  })
```

**状态相关的重要概念：**

1. **settled（已敲定）**：Promise已经fulfilled或rejected，不再是pending
2. **thenable**：具有then方法的对象，可以被Promise.resolve()处理
3. **状态检测**：无法直接同步检测Promise状态，需要通过then/catch异步处理

**常见面试追问：**

```javascript
// Q: 以下代码的执行结果是什么？
const p = new Promise((resolve, reject) => {
  resolve('first')
  resolve('second') // 无效
  reject('error') // 无效
})

p.then(value => console.log(value)) // 输出: "first"

// Q: Promise状态改变是同步还是异步的？
const p2 = new Promise(resolve => {
  console.log('1')
  resolve('2')
  console.log('3')
})
console.log('4')
p2.then(value => console.log(value))
console.log('5')
// 输出顺序: 1, 3, 4, 5, 2
```

**记忆要点总结：**

- 三种状态：pending（等待）→ fulfilled（成功）/rejected（失败）
- 状态转换不可逆，一次性的
- settled = fulfilled + rejected（已敲定状态）
- 状态改变是同步的，但then/catch回调是异步的
- 多次调用resolve/reject只有第一次有效

# **118. [中级]** 如何创建一个Promise？

- new Promise
- Promise()

## 深度分析与补充

**问题本质解读：** 这道题考察Promise的创建方式和构造函数的使用，面试官想了解你是否掌握Promise的各种创建方法和使用场景。

**技术错误纠正：**

- `Promise()`是错误的，Promise必须使用new关键字调用
- 除了new Promise外，还有多种其他创建方式

**知识点系统梳理：**

**Promise创建的多种方式：**

1. **new Promise(executor)** - 基础构造方式
2. **Promise.resolve(value)** - 创建已成功的Promise
3. **Promise.reject(reason)** - 创建已失败的Promise
4. **Promise.all(iterable)** - 组合多个Promise
5. **Promise.race(iterable)** - 竞争多个Promise
6. **Promise.allSettled(iterable)** - 等待所有Promise完成
7. **Promise.any(iterable)** - 任意一个成功即可

**实战应用举例：**

```javascript
// 1. 基础构造方式 - new Promise
const basicPromise = new Promise((resolve, reject) => {
  // executor函数立即执行
  const success = Math.random() > 0.5

  if (success) {
    resolve('操作成功')
  } else {
    reject(new Error('操作失败'))
  }
})

// 2. 创建已成功的Promise
const resolvedPromise = Promise.resolve('立即成功')
const resolvedWithPromise = Promise.resolve(basicPromise) // 如果参数是Promise，直接返回

// 3. 创建已失败的Promise
const rejectedPromise = Promise.reject(new Error('立即失败'))

// 4. 从回调函数转换为Promise（Promisify）
function promisify(fn) {
  return function (...args) {
    return new Promise((resolve, reject) => {
      fn(...args, (err, result) => {
        if (err) {
          reject(err)
        } else {
          resolve(result)
        }
      })
    })
  }
}

// 使用示例
const fs = require('fs')
const readFileAsync = promisify(fs.readFile)

// 5. 延迟Promise（常用于测试和演示）
function delay(ms) {
  return new Promise(resolve => {
    setTimeout(resolve, ms)
  })
}

// 6. 条件Promise创建
function createConditionalPromise(condition, value, error) {
  if (condition) {
    return Promise.resolve(value)
  } else {
    return Promise.reject(error)
  }
}

// 7. 从异步函数创建Promise
async function createFromAsync() {
  // async函数自动返回Promise
  await delay(1000)
  return '异步操作完成'
}

// 8. 链式Promise创建
function createChainedPromise(initialValue) {
  return Promise.resolve(initialValue)
    .then(value => value * 2)
    .then(value => value + 10)
    .then(value => `结果: ${value}`)
}

// 9. 错误处理的Promise创建
function createWithErrorHandling(operation) {
  return new Promise((resolve, reject) => {
    try {
      const result = operation()
      if (result instanceof Promise) {
        result.then(resolve).catch(reject)
      } else {
        resolve(result)
      }
    } catch (error) {
      reject(error)
    }
  })
}

// 10. 超时Promise
function createTimeoutPromise(promise, timeout) {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(`操作超时: ${timeout}ms`))
    }, timeout)
  })

  return Promise.race([promise, timeoutPromise])
}

// 使用示例
const timeoutExample = createTimeoutPromise(
  delay(2000).then(() => '操作完成'),
  1500,
) // 1.5秒后超时
```

**创建Promise的最佳实践：**

1. **明确executor函数的同步性**：executor立即执行，不要在其中放置异步操作的逻辑
2. **正确处理异常**：在executor中使用try-catch捕获同步异常
3. **避免Promise构造函数反模式**：不要在已有Promise的基础上再包装Promise
4. **选择合适的创建方式**：根据场景选择最简洁的创建方法

**常见错误和陷阱：**

```javascript
// ❌ 错误：Promise构造函数反模式
function badExample() {
  return new Promise((resolve, reject) => {
    someAsyncFunction()
      .then(resolve) // 应该直接返回someAsyncFunction()
      .catch(reject)
  })
}

// ✅ 正确：直接返回Promise
function goodExample() {
  return someAsyncFunction()
}

// ❌ 错误：忘记处理异常
const riskyPromise = new Promise((resolve, reject) => {
  const result = JSON.parse(invalidJson) // 可能抛出异常
  resolve(result)
})

// ✅ 正确：处理异常
const safePromise = new Promise((resolve, reject) => {
  try {
    const result = JSON.parse(invalidJson)
    resolve(result)
  } catch (error) {
    reject(error)
  }
})
```

**记忆要点总结：**

- 主要创建方式：new Promise(executor)
- 快捷方式：Promise.resolve()、Promise.reject()
- 组合方式：Promise.all()、Promise.race()等
- executor函数立即执行，接收resolve和reject参数
- 必须使用new关键字，不能直接调用Promise()
- 根据场景选择最合适的创建方式

# **119. [中级]** Promise.then()方法的用法

- pormise.then() 参数是一个执行器方法，返回一个promise.thenable。
- 通常为：(resolve,reject) =>{ 执行成功的resolve 或者失败的reject }
- 当执行器函数只有一个参数时，默认为：resolve

## 深度分析与补充

**问题本质解读：** 这道题考察Promise.then()方法的核心机制，面试官想了解你是否理解then方法的参数、返回值和链式调用原理。

**技术错误纠正：**

- then()方法的参数不是"执行器方法"，而是回调函数
- then()接收两个参数：onFulfilled和onRejected，不是resolve和reject
- then()返回新的Promise，不是"promise.thenable"

**知识点系统梳理：**

**Promise.then()的完整语法：**

```javascript
promise.then(onFulfilled, onRejected)
```

**参数说明：**

1. **onFulfilled**：Promise成功时的回调函数，接收resolve的值
2. **onRejected**：Promise失败时的回调函数，接收reject的原因（可选）

**返回值：**

- 总是返回一个新的Promise对象，支持链式调用

**实战应用举例：**

```javascript
// 1. 基础用法
const promise = new Promise((resolve, reject) => {
  setTimeout(() => {
    resolve('初始数据')
  }, 1000)
})

// 只处理成功情况
promise.then(value => {
  console.log('成功:', value) // "成功: 初始数据"
  return value.toUpperCase()
})

// 同时处理成功和失败
promise.then(
  value => {
    console.log('成功:', value)
    return value.toUpperCase()
  },
  error => {
    console.log('失败:', error)
    return '默认值'
  },
)

// 2. 链式调用详解
Promise.resolve(10)
  .then(value => {
    console.log('第一步:', value) // 10
    return value * 2 // 返回普通值
  })
  .then(value => {
    console.log('第二步:', value) // 20
    return Promise.resolve(value + 5) // 返回Promise
  })
  .then(value => {
    console.log('第三步:', value) // 25
    throw new Error('故意抛出错误') // 抛出异常
  })
  .then(
    value => {
      console.log('不会执行') // 不会执行
    },
    error => {
      console.log('捕获错误:', error.message) // "故意抛出错误"
      return '恢复正常'
    },
  )
  .then(value => {
    console.log('最后一步:', value) // "恢复正常"
  })

// 3. then方法的返回值处理
function demonstrateThenReturns() {
  // 返回普通值
  Promise.resolve('hello')
    .then(value => {
      return value + ' world' // 新Promise resolve为 "hello world"
    })
    .then(value => console.log(value)) // "hello world"

  // 返回Promise
  Promise.resolve('start')
    .then(value => {
      return new Promise(resolve => {
        setTimeout(() => resolve(value + ' delayed'), 1000)
      })
    })
    .then(value => console.log(value)) // 1秒后输出 "start delayed"

  // 不返回任何值（undefined）
  Promise.resolve('data')
    .then(value => {
      console.log(value) // "data"
      // 没有return语句
    })
    .then(value => console.log(value)) // undefined

  // 抛出异常
  Promise.resolve('normal')
    .then(value => {
      throw new Error('something wrong')
    })
    .then(
      value => console.log('不会执行'),
      error => console.log('错误:', error.message),
    )
}

// 4. 错误处理的传播
Promise.reject('初始错误')
  .then(
    value => console.log('成功:', value),
    error => {
      console.log('处理错误:', error) // "处理错误: 初始错误"
      return '已修复' // 返回正常值，后续then会成功
    },
  )
  .then(value => {
    console.log('恢复正常:', value) // "恢复正常: 已修复"
  })

// 5. then的异步特性
console.log('1')
Promise.resolve('2').then(value => console.log(value))
console.log('3')
// 输出顺序: 1, 3, 2

// 6. 复杂的链式调用场景
function fetchUserProfile(userId) {
  return fetch(`/api/users/${userId}`)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      return response.json()
    })
    .then(user => {
      // 获取用户详细信息
      return Promise.all([
        user,
        fetch(`/api/users/${user.id}/profile`).then(r => r.json()),
        fetch(`/api/users/${user.id}/settings`).then(r => r.json()),
      ])
    })
    .then(([user, profile, settings]) => {
      return {
        ...user,
        profile,
        settings,
        lastUpdated: new Date().toISOString(),
      }
    })
    .catch(error => {
      console.error('获取用户信息失败:', error)
      return null // 返回默认值
    })
}
```

**then方法的重要特性：**

1. **异步执行**：then的回调总是异步执行，即使Promise已经settled
2. **值穿透**：如果onFulfilled不是函数，值会穿透到下一个then
3. **错误传播**：如果onRejected不是函数，错误会传播到下一个catch
4. **新Promise返回**：每次调用then都返回新的Promise对象

**常见使用模式：**

```javascript
// 值穿透示例
Promise.resolve('hello')
  .then(null) // 值穿透
  .then(value => console.log(value)) // "hello"

// 错误传播示例
Promise.reject('error')
  .then(value => console.log(value)) // 跳过
  .then(value => console.log(value)) // 跳过
  .catch(error => console.log('捕获:', error)) // "捕获: error"

// 条件处理
function processData(data) {
  return Promise.resolve(data)
    .then(data => {
      if (!data) {
        throw new Error('数据为空')
      }
      return data
    })
    .then(data => {
      // 数据处理逻辑
      return data.map(item => ({ ...item, processed: true }))
    })
}
```

**记忆要点总结：**

- then(onFulfilled, onRejected)：两个回调函数参数
- 返回新Promise，支持链式调用
- onFulfilled接收resolve值，onRejected接收reject原因
- 回调函数的返回值决定新Promise的状态
- 异步执行，支持值穿透和错误传播
- 是Promise链式调用的核心方法

# **120. [中级]** Promise的链式调用如何工作？

- 通过返回一个promise的thenable 在下一个then中接收到上一个then的结果，继续执行下一步的操作

## 深度分析与补充

**问题本质解读：** 这道题考察Promise链式调用的内部机制，面试官想了解你是否理解Promise链的工作原理、值传递和状态转换规则。

**技术错误纠正：**

- 不是"promise的thenable"，而是每个then方法都返回一个新的Promise对象
- 链式调用的核心是Promise的状态传递和值转换机制

**知识点系统梳理：**

**Promise链式调用的工作原理：**

1. **新Promise返回**：每个then/catch/finally都返回新的Promise
2. **值传递规则**：前一个Promise的结果决定下一个Promise的状态
3. **异步队列**：所有then回调都在微任务队列中异步执行
4. **状态传播**：成功/失败状态沿着链条传播

**值传递的四种情况：**

1. **返回普通值**：新Promise以该值resolve
2. **返回Promise**：新Promise的状态跟随返回的Promise
3. **抛出异常**：新Promise以该异常reject
4. **无返回值**：新Promise以undefined resolve

**实战应用举例：**

```javascript
// 1. 基础链式调用机制演示
function demonstrateChaining() {
  console.log('=== Promise链式调用机制 ===')

  const promise1 = Promise.resolve('初始值')
  console.log('promise1:', promise1) // Promise对象

  const promise2 = promise1.then(value => {
    console.log('第一个then接收:', value) // "初始值"
    return value + ' -> 处理后'
  })
  console.log('promise2:', promise2) // 新的Promise对象
  console.log('promise1 === promise2:', promise1 === promise2) // false

  const promise3 = promise2.then(value => {
    console.log('第二个then接收:', value) // "初始值 -> 处理后"
    return value + ' -> 再次处理'
  })
  console.log('promise3:', promise3) // 又一个新的Promise对象

  return promise3
}

// 2. 详细的值传递示例
function valuePassingDemo() {
  console.log('=== 值传递机制 ===')

  // 情况1: 返回普通值
  Promise.resolve(10)
    .then(value => {
      console.log('接收到:', value) // 10
      return value * 2 // 返回普通值20
    })
    .then(value => {
      console.log('新Promise resolve为:', value) // 20
    })

  // 情况2: 返回Promise
  Promise.resolve('start')
    .then(value => {
      console.log('接收到:', value) // "start"
      return new Promise(resolve => {
        setTimeout(() => resolve(value + ' delayed'), 1000)
      })
    })
    .then(value => {
      console.log('1秒后接收到:', value) // "start delayed"
    })

  // 情况3: 抛出异常
  Promise.resolve('normal')
    .then(value => {
      console.log('正常处理:', value) // "normal"
      throw new Error('故意出错')
    })
    .then(
      value => {
        console.log('不会执行') // 跳过
      },
      error => {
        console.log('捕获异常:', error.message) // "故意出错"
        return '错误已处理' // 恢复正常流程
      },
    )
    .then(value => {
      console.log('恢复正常:', value) // "错误已处理"
    })

  // 情况4: 无返回值
  Promise.resolve('data')
    .then(value => {
      console.log('处理数据:', value) // "data"
      // 没有return语句
    })
    .then(value => {
      console.log('接收到undefined:', value) // undefined
    })
}

// 3. 复杂的链式调用场景
function complexChainExample() {
  console.log('=== 复杂链式调用 ===')

  return Promise.resolve({ userId: 123 })
    .then(data => {
      console.log('1. 获取用户ID:', data.userId)
      // 模拟异步获取用户信息
      return fetch(`/api/users/${data.userId}`)
    })
    .then(response => {
      console.log('2. 检查响应状态')
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      return response.json()
    })
    .then(user => {
      console.log('3. 解析用户数据:', user.name)
      // 并行获取相关数据
      return Promise.all([
        user,
        fetch(`/api/users/${user.id}/posts`).then(r => r.json()),
        fetch(`/api/users/${user.id}/friends`).then(r => r.json()),
      ])
    })
    .then(([user, posts, friends]) => {
      console.log('4. 组合所有数据')
      return {
        user,
        posts: posts.slice(0, 5), // 只取前5篇文章
        friendsCount: friends.length,
        lastUpdated: new Date().toISOString(),
      }
    })
    .catch(error => {
      console.error('链式调用中的错误:', error)
      // 返回默认数据
      return {
        user: null,
        posts: [],
        friendsCount: 0,
        error: error.message,
      }
    })
    .finally(() => {
      console.log('5. 链式调用完成')
    })
}

// 4. 错误传播机制
function errorPropagationDemo() {
  console.log('=== 错误传播机制 ===')

  Promise.resolve('开始')
    .then(value => {
      console.log('步骤1:', value)
      return value + ' -> 步骤1完成'
    })
    .then(value => {
      console.log('步骤2:', value)
      throw new Error('步骤2出错')
    })
    .then(value => {
      console.log('步骤3: 不会执行') // 跳过
      return value + ' -> 步骤3完成'
    })
    .then(value => {
      console.log('步骤4: 也不会执行') // 跳过
    })
    .catch(error => {
      console.log('错误处理:', error.message) // "步骤2出错"
      return '错误已修复'
    })
    .then(value => {
      console.log('恢复执行:', value) // "错误已修复"
    })
}

// 5. 条件链式调用
function conditionalChaining(shouldProcess) {
  return Promise.resolve('原始数据')
    .then(data => {
      if (shouldProcess) {
        return processData(data)
      }
      return data // 跳过处理
    })
    .then(data => {
      return validateData(data)
    })
    .then(data => {
      return saveData(data)
    })
}

function processData(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(data + ' [已处理]')
    }, 100)
  })
}

function validateData(data) {
  if (!data || data.length === 0) {
    throw new Error('数据验证失败')
  }
  return data + ' [已验证]'
}

function saveData(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(data + ' [已保存]')
    }, 100)
  })
}
```

**链式调用的关键特性：**

1. **每个then返回新Promise**：保证链式调用的连续性
2. **异步执行**：所有回调都在微任务队列中执行
3. **状态传播**：错误会跳过后续的成功回调，直到遇到错误处理
4. **值转换**：支持同步值、Promise、异常的自动处理

**常见使用模式：**

```javascript
// 数据处理管道
function createDataPipeline(data) {
  return Promise.resolve(data)
    .then(validateInput)
    .then(transformData)
    .then(enrichData)
    .then(formatOutput)
    .catch(handleError)
}

// 重试机制
function retryOperation(operation, maxRetries = 3) {
  return operation().catch(error => {
    if (maxRetries > 0) {
      console.log(`重试剩余次数: ${maxRetries}`)
      return retryOperation(operation, maxRetries - 1)
    }
    throw error
  })
}

// 超时控制
function withTimeout(promise, timeout) {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('操作超时')), timeout)
  })

  return Promise.race([promise, timeoutPromise])
}
```

**记忆要点总结：**

- 每个then/catch/finally都返回新的Promise对象
- 前一个Promise的结果决定下一个Promise的状态
- 支持四种值传递：普通值、Promise、异常、undefined
- 错误会沿链传播，直到被catch捕获
- 所有回调都异步执行，保证执行顺序
- 链式调用是Promise强大功能的核心体现

# **121. [中级]** Promise.catch()和try-catch的区别

- promise.catch 捕获Promise中异步操作的异常和then中异常
- 包含在promise外部的try catch 可以捕获promise执行中的异常

## 深度分析与补充

**问题本质解读：** 这道题考察异步错误处理机制的差异，面试官想了解你是否理解同步和异步错误处理的不同方式和适用场景。

**技术错误纠正：**

- try-catch无法直接捕获Promise内部的异步异常
- 需要区分Promise构造函数中的同步异常和异步异常

**知识点系统梳理：**

**Promise.catch()特点：**

1. **异步错误处理**：专门处理Promise链中的异步错误
2. **错误传播**：捕获整个Promise链中的错误
3. **返回新Promise**：支持错误恢复和继续链式调用
4. **微任务执行**：在微任务队列中异步执行

**try-catch特点：**

1. **同步错误处理**：只能捕获同步代码中的异常
2. **立即执行**：同步捕获和处理异常
3. **无法处理异步**：对Promise内部异步异常无效
4. **阻塞执行**：异常会中断当前执行流程

**实战应用举例：**

```javascript
// 1. Promise.catch() vs try-catch 基础对比
console.log('=== 基础对比 ===')

// Promise.catch() - 正确捕获异步异常
Promise.resolve('data')
  .then(value => {
    throw new Error('异步操作中的错误')
  })
  .catch(error => {
    console.log('Promise.catch捕获:', error.message) // 能够捕获
  })

// try-catch - 无法捕获Promise异步异常
try {
  Promise.resolve('data').then(value => {
    throw new Error('异步操作中的错误')
  })
} catch (error) {
  console.log('try-catch捕获:', error.message) // 不会执行
}

// 2. 不同场景的错误处理
function errorHandlingScenarios() {
  console.log('=== 错误处理场景 ===')

  // 场景1: Promise构造函数中的同步异常
  try {
    const promise = new Promise((resolve, reject) => {
      throw new Error('构造函数中的同步异常') // try-catch可以捕获
    })

    promise.catch(error => {
      console.log('Promise.catch也能捕获构造函数异常:', error.message)
    })
  } catch (error) {
    console.log('try-catch捕获构造函数异常:', error.message)
  }

  // 场景2: Promise构造函数中的异步异常
  try {
    const promise = new Promise((resolve, reject) => {
      setTimeout(() => {
        throw new Error('构造函数中的异步异常') // try-catch无法捕获
      }, 100)
    })

    promise.catch(error => {
      console.log('Promise.catch捕获异步异常:', error.message) // 也无法捕获
    })
  } catch (error) {
    console.log('try-catch无法捕获异步异常') // 不会执行
  }

  // 正确处理异步异常的方式
  const correctPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // 可能出错的异步操作
        const result = riskyAsyncOperation()
        resolve(result)
      } catch (error) {
        reject(error) // 正确的异步错误处理
      }
    }, 100)
  })

  correctPromise.catch(error => {
    console.log('正确捕获异步异常:', error.message)
  })
}

// 3. async/await中的错误处理
async function asyncAwaitErrorHandling() {
  console.log('=== async/await错误处理 ===')

  // try-catch在async函数中可以捕获await的异常
  try {
    const result = await Promise.reject(new Error('await异常'))
    console.log(result)
  } catch (error) {
    console.log('async/await中try-catch捕获:', error.message)
  }

  // 混合使用
  try {
    const data = await fetchData()
    const processed = await processData(data)
    return processed
  } catch (error) {
    console.log('处理过程中的异常:', error.message)
    throw error // 重新抛出或处理
  }
}

// 4. 错误处理的最佳实践
function errorHandlingBestPractices() {
  console.log('=== 错误处理最佳实践 ===')

  // 方式1: Promise链式错误处理
  function promiseChainApproach() {
    return fetchUserData()
      .then(validateData)
      .then(processData)
      .then(saveData)
      .catch(error => {
        console.error('Promise链中的错误:', error)
        return handleError(error)
      })
  }

  // 方式2: async/await错误处理
  async function asyncAwaitApproach() {
    try {
      const userData = await fetchUserData()
      const validData = await validateData(userData)
      const processedData = await processData(validData)
      const result = await saveData(processedData)
      return result
    } catch (error) {
      console.error('async/await中的错误:', error)
      return handleError(error)
    }
  }

  // 方式3: 混合错误处理
  async function hybridApproach() {
    try {
      const userData = await fetchUserData().catch(error => {
        console.log('获取用户数据失败，使用默认数据')
        return getDefaultUserData()
      })

      const result = await processData(userData)
      return result
    } catch (error) {
      console.error('处理过程中的错误:', error)
      throw error
    }
  }
}

// 5. 全局错误处理
function globalErrorHandling() {
  console.log('=== 全局错误处理 ===')

  // 未捕获的Promise异常
  window.addEventListener('unhandledrejection', event => {
    console.error('未处理的Promise异常:', event.reason)
    event.preventDefault() // 阻止默认的错误处理
  })

  // 未捕获的同步异常
  window.addEventListener('error', event => {
    console.error('未处理的同步异常:', event.error)
  })

  // 示例：未处理的Promise异常
  Promise.reject(new Error('未处理的异常')) // 会触发unhandledrejection事件
}

// 辅助函数
function fetchUserData() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const success = Math.random() > 0.3
      if (success) {
        resolve({ id: 1, name: 'John' })
      } else {
        reject(new Error('网络请求失败'))
      }
    }, 100)
  })
}

function validateData(data) {
  if (!data || !data.id) {
    throw new Error('数据验证失败')
  }
  return data
}

function processData(data) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (data.name) {
        resolve({ ...data, processed: true })
      } else {
        reject(new Error('数据处理失败'))
      }
    }, 50)
  })
}

function saveData(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({ ...data, saved: true, timestamp: Date.now() })
    }, 50)
  })
}

function handleError(error) {
  return {
    error: true,
    message: error.message,
    timestamp: Date.now(),
  }
}

function getDefaultUserData() {
  return { id: 0, name: 'Guest', default: true }
}

function riskyAsyncOperation() {
  throw new Error('异步操作失败')
}
```

**使用场景对比：**

| 场景          | Promise.catch()       | try-catch    |
| ------------- | --------------------- | ------------ |
| Promise链错误 | ✅ 推荐               | ❌ 无效      |
| async/await   | ✅ 可用               | ✅ 推荐      |
| 同步代码异常  | ❌ 无效               | ✅ 推荐      |
| 异步回调异常  | ✅ 需配合reject       | ❌ 无效      |
| 全局错误处理  | ✅ unhandledrejection | ✅ error事件 |

**记忆要点总结：**

- Promise.catch()：处理异步Promise链中的错误
- try-catch：处理同步代码和async/await中的错误
- Promise构造函数中的同步异常两者都能捕获
- Promise构造函数中的异步异常需要手动reject
- async/await让try-catch可以处理异步异常
- 选择合适的错误处理方式提高代码健壮性

# **122. [中级]** 如何处理多个Promise？

- Promise.all([]) 可以通过数组的方式将多个异步操作同时并行请求，只有全部成功才成功，有一个失败都失败。返回全部请求执行结果
- Promise.race([]) 可以通过数组的方式将多个异步操作同时竞争请求，只有全部失败才都失败，返回最快完成请求的那个结果
- Promise.allSettled([]) 可以通过数组的方式将多个异步操作同时并行请求,返回各个请求的结果（结果中包含结果状态和值）
- Promise.any([]) 可以通过数组的方式将多个异步操作同时竞争请求, 只要有一个请求成功，返回最先成功的那个，只有全部失败的时候才失败

## 深度分析与补充

**问题本质解读：** 这道题考察Promise并发处理的各种方法，面试官想了解你是否掌握不同场景下的Promise组合策略和性能优化。

**技术错误纠正：**

- Promise.race()不是"只有全部失败才都失败"，而是返回第一个settled的Promise结果
- 需要明确各个方法的具体行为差异和使用场景

**知识点系统梳理：**

**四种Promise组合方法对比：**

| 方法                 | 成功条件 | 失败条件   | 返回结果     | 使用场景  |
| -------------------- | -------- | ---------- | ------------ | --------- |
| Promise.all()        | 全部成功 | 任一失败   | 成功结果数组 | 全部依赖  |
| Promise.race()       | 任一完成 | 第一个失败 | 第一个结果   | 竞速/超时 |
| Promise.allSettled() | 全部完成 | 不会失败   | 状态结果数组 | 批量处理  |
| Promise.any()        | 任一成功 | 全部失败   | 第一个成功   | 容错处理  |

**实战应用举例：**

```javascript
// 1. Promise.all() - 全部成功才成功
async function demonstratePromiseAll() {
  console.log('=== Promise.all() 演示 ===')

  const promises = [
    fetch('/api/user/1').then(r => r.json()),
    fetch('/api/user/2').then(r => r.json()),
    fetch('/api/user/3').then(r => r.json()),
  ]

  try {
    const results = await Promise.all(promises)
    console.log('所有用户数据:', results)
    // 结果按原数组顺序返回，不是完成顺序
  } catch (error) {
    console.log('任一请求失败:', error)
    // 只要有一个失败，整个Promise.all就失败
  }

  // 实际应用：并行获取相关数据
  async function getUserProfile(userId) {
    const [user, posts, friends] = await Promise.all([
      fetchUser(userId),
      fetchUserPosts(userId),
      fetchUserFriends(userId),
    ])

    return { user, posts, friends }
  }
}

// 2. Promise.race() - 第一个完成的结果
async function demonstratePromiseRace() {
  console.log('=== Promise.race() 演示 ===')

  const promises = [
    new Promise(resolve => setTimeout(() => resolve('慢速结果'), 2000)),
    new Promise(resolve => setTimeout(() => resolve('快速结果'), 1000)),
    new Promise((_, reject) => setTimeout(() => reject('错误'), 1500)),
  ]

  try {
    const result = await Promise.race(promises)
    console.log('最快完成:', result) // "快速结果"
  } catch (error) {
    console.log('最快失败:', error)
  }

  // 实际应用：请求超时控制
  function withTimeout(promise, timeout) {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), timeout)
    })

    return Promise.race([promise, timeoutPromise])
  }

  // 使用示例
  try {
    const data = await withTimeout(fetch('/api/slow-endpoint'), 5000)
    console.log('请求成功:', data)
  } catch (error) {
    console.log('请求超时或失败:', error.message)
  }
}

// 3. Promise.allSettled() - 等待全部完成
async function demonstratePromiseAllSettled() {
  console.log('=== Promise.allSettled() 演示 ===')

  const promises = [
    Promise.resolve('成功1'),
    Promise.reject('失败1'),
    Promise.resolve('成功2'),
    Promise.reject('失败2'),
  ]

  const results = await Promise.allSettled(promises)
  console.log('所有结果:', results)
  // [
  //   { status: 'fulfilled', value: '成功1' },
  //   { status: 'rejected', reason: '失败1' },
  //   { status: 'fulfilled', value: '成功2' },
  //   { status: 'rejected', reason: '失败2' }
  // ]

  // 处理结果
  const successful = results
    .filter(result => result.status === 'fulfilled')
    .map(result => result.value)

  const failed = results.filter(result => result.status === 'rejected').map(result => result.reason)

  console.log('成功的:', successful)
  console.log('失败的:', failed)

  // 实际应用：批量操作
  async function batchUpdateUsers(userIds) {
    const updatePromises = userIds.map(id => updateUser(id))
    const results = await Promise.allSettled(updatePromises)

    const summary = {
      total: results.length,
      successful: results.filter(r => r.status === 'fulfilled').length,
      failed: results.filter(r => r.status === 'rejected').length,
      errors: results.filter(r => r.status === 'rejected').map(r => r.reason),
    }

    return summary
  }
}

// 4. Promise.any() - 任一成功即可
async function demonstratePromiseAny() {
  console.log('=== Promise.any() 演示 ===')

  const promises = [
    Promise.reject('服务器1失败'),
    Promise.reject('服务器2失败'),
    Promise.resolve('服务器3成功'),
    Promise.resolve('服务器4成功'),
  ]

  try {
    const result = await Promise.any(promises)
    console.log('第一个成功:', result) // "服务器3成功"
  } catch (error) {
    console.log('全部失败:', error) // AggregateError
  }

  // 实际应用：多服务器容错
  async function fetchFromMultipleServers(endpoint) {
    const servers = [
      'https://api1.example.com',
      'https://api2.example.com',
      'https://api3.example.com',
    ]

    const promises = servers.map(server => fetch(`${server}${endpoint}`).then(r => r.json()))

    try {
      return await Promise.any(promises)
    } catch (error) {
      throw new Error('所有服务器都不可用')
    }
  }
}

// 5. 复杂组合场景
async function complexCombinationScenarios() {
  console.log('=== 复杂组合场景 ===')

  // 场景1: 部分依赖 + 容错
  async function fetchUserDashboard(userId) {
    // 必需数据：用户信息（必须成功）
    const userPromise = fetchUser(userId)

    // 可选数据：统计信息（可以失败）
    const optionalPromises = [
      fetchUserStats(userId).catch(() => null),
      fetchUserNotifications(userId).catch(() => []),
      fetchUserRecommendations(userId).catch(() => []),
    ]

    const [user, ...optionalData] = await Promise.all([userPromise, ...optionalPromises])

    const [stats, notifications, recommendations] = optionalData

    return {
      user,
      stats,
      notifications,
      recommendations,
    }
  }

  // 场景2: 分层加载
  async function layeredLoading() {
    // 第一层：关键数据
    const criticalData = await Promise.all([fetchCriticalData1(), fetchCriticalData2()])

    // 第二层：重要数据（基于第一层）
    const importantPromises = criticalData.map(data => fetchImportantData(data.id))
    const importantData = await Promise.allSettled(importantPromises)

    // 第三层：可选数据（并行加载）
    const optionalPromises = [fetchOptionalData1(), fetchOptionalData2(), fetchOptionalData3()]
    const optionalData = await Promise.allSettled(optionalPromises)

    return {
      critical: criticalData,
      important: importantData,
      optional: optionalData,
    }
  }

  // 场景3: 智能重试
  async function smartRetry(operation, maxRetries = 3) {
    const attempts = Array.from(
      { length: maxRetries },
      (_, i) =>
        new Promise(resolve => {
          setTimeout(() => {
            operation()
              .then(resolve)
              .catch(error => resolve(Promise.reject(error)))
          }, i * 1000) // 递增延迟
        }),
    )

    try {
      return await Promise.any(attempts)
    } catch (error) {
      throw new Error(`操作失败，已重试${maxRetries}次`)
    }
  }
}

// 辅助函数
function fetchUser(id) {
  return new Promise(resolve => {
    setTimeout(() => resolve({ id, name: `User${id}` }), 100)
  })
}

function fetchUserPosts(id) {
  return new Promise(resolve => {
    setTimeout(() => resolve([`Post1 by User${id}`, `Post2 by User${id}`]), 150)
  })
}

function fetchUserFriends(id) {
  return new Promise(resolve => {
    setTimeout(() => resolve([`Friend1 of User${id}`, `Friend2 of User${id}`]), 120)
  })
}

function updateUser(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (Math.random() > 0.3) {
        resolve(`User${id} updated`)
      } else {
        reject(`Failed to update User${id}`)
      }
    }, 100)
  })
}
```

**使用场景选择指南：**

1. **Promise.all()**: 所有操作都必须成功，如获取用户完整信息
2. **Promise.race()**: 需要最快响应，如请求超时控制
3. **Promise.allSettled()**: 批量操作，需要知道每个结果，如批量更新
4. **Promise.any()**: 容错处理，任一成功即可，如多服务器请求

**记忆要点总结：**

- Promise.all()：全部成功才成功，快速失败
- Promise.race()：第一个完成就返回，竞速机制
- Promise.allSettled()：等待全部完成，不会失败
- Promise.any()：任一成功即可，全部失败才失败
- 根据业务需求选择合适的组合方法
- 注意错误处理和性能优化

# **123. [中级]** Promise.all()和Promise.allSettled()的区别

- Promise.all([]) 可以通过数组的方式将多个异步操作同时并行请求，只有全部成功才成功，有一个失败都失败。返回全部请求执行结果
- Promise.allSettled([]) 可以通过数组的方式将多个异步操作同时并行请求,返回各个请求的结果（结果中包含结果状态和值）
  1. 整体完成的时机不同：
     1. all 只要其中有一个失败就都失败
     2. allSettled 不论其中的失败和成功 全部执行
  2. 返回的结果的结构不同：
     1. all 根据数组顺序返回对应顺序的结果
     2. allSettled 根据数组顺序返回对应顺序的对象内容 包括了完成状态 stuas 和请求结果 value

## 深度分析与补充

**问题本质解读：** 这道题考察两种Promise组合方法的核心差异，面试官想了解你是否理解不同错误处理策略的适用场景。

**技术错误纠正：**

- "stuas"应该是"status"
- 需要明确两者在错误处理、返回值结构、使用场景上的具体差异

**知识点系统梳理：**

**核心差异对比：**

| 特性     | Promise.all()         | Promise.allSettled() |
| -------- | --------------------- | -------------------- |
| 失败策略 | 快速失败（fail-fast） | 等待全部完成         |
| 返回时机 | 全部成功或首个失败    | 全部Promise settled  |
| 返回结果 | 成功值数组            | 状态对象数组         |
| 错误处理 | 抛出异常              | 不会抛出异常         |
| 适用场景 | 全部依赖              | 批量处理             |

**实战应用举例：**

```javascript
// 1. 基础行为对比
async function basicComparison() {
  console.log('=== Promise.all() vs Promise.allSettled() 基础对比 ===')

  const promises = [
    Promise.resolve('成功1'),
    Promise.reject('失败1'),
    Promise.resolve('成功2'),
    new Promise(resolve => setTimeout(() => resolve('延迟成功'), 1000)),
  ]

  // Promise.all() - 快速失败
  try {
    const allResults = await Promise.all(promises)
    console.log('Promise.all 结果:', allResults) // 不会执行
  } catch (error) {
    console.log('Promise.all 失败:', error) // "失败1"
  }

  // Promise.allSettled() - 等待全部完成
  const settledResults = await Promise.allSettled(promises)
  console.log('Promise.allSettled 结果:', settledResults)
  // [
  //   { status: 'fulfilled', value: '成功1' },
  //   { status: 'rejected', reason: '失败1' },
  //   { status: 'fulfilled', value: '成功2' },
  //   { status: 'fulfilled', value: '延迟成功' }
  // ]
}

// 2. 实际应用场景对比
async function practicalScenarios() {
  console.log('=== 实际应用场景对比 ===')

  // 场景1: 用户资料页面 - 使用Promise.all()
  // 所有数据都是必需的，任一失败都应该显示错误页面
  async function loadUserProfilePage(userId) {
    try {
      const [user, profile, settings] = await Promise.all([
        fetchUser(userId), // 必需：用户基本信息
        fetchUserProfile(userId), // 必需：用户详细资料
        fetchUserSettings(userId), // 必需：用户设置
      ])

      return {
        success: true,
        data: { user, profile, settings },
      }
    } catch (error) {
      return {
        success: false,
        error: '加载用户信息失败',
        details: error.message,
      }
    }
  }

  // 场景2: 数据同步 - 使用Promise.allSettled()
  // 需要知道每个操作的结果，部分失败不影响其他操作
  async function syncUserData(userId) {
    const syncOperations = [
      syncUserPosts(userId),
      syncUserPhotos(userId),
      syncUserContacts(userId),
      syncUserSettings(userId),
    ]

    const results = await Promise.allSettled(syncOperations)

    const summary = {
      total: results.length,
      successful: 0,
      failed: 0,
      details: [],
    }

    results.forEach((result, index) => {
      const operation = ['posts', 'photos', 'contacts', 'settings'][index]

      if (result.status === 'fulfilled') {
        summary.successful++
        summary.details.push({
          operation,
          status: 'success',
          data: result.value,
        })
      } else {
        summary.failed++
        summary.details.push({
          operation,
          status: 'failed',
          error: result.reason,
        })
      }
    })

    return summary
  }
}

// 3. 错误处理策略对比
async function errorHandlingComparison() {
  console.log('=== 错误处理策略对比 ===')

  // Promise.all() 的错误处理
  async function allErrorHandling() {
    const promises = [fetchData('endpoint1'), fetchData('endpoint2'), fetchData('endpoint3')]

    try {
      const results = await Promise.all(promises)
      console.log('所有请求成功:', results)
      return { success: true, data: results }
    } catch (error) {
      console.log('有请求失败，全部中止:', error)
      // 无法知道哪些成功了，哪些失败了
      return { success: false, error: error.message }
    }
  }

  // Promise.allSettled() 的错误处理
  async function allSettledErrorHandling() {
    const promises = [fetchData('endpoint1'), fetchData('endpoint2'), fetchData('endpoint3')]

    const results = await Promise.allSettled(promises)

    const successful = results
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value)

    const failed = results
      .filter(result => result.status === 'rejected')
      .map((result, index) => ({
        index,
        error: result.reason,
      }))

    console.log('成功的请求:', successful)
    console.log('失败的请求:', failed)

    return {
      success: failed.length === 0,
      data: successful,
      errors: failed,
      partial: successful.length > 0 && failed.length > 0,
    }
  }
}

// 4. 性能和资源使用对比
async function performanceComparison() {
  console.log('=== 性能和资源使用对比 ===')

  // Promise.all() - 快速失败，节省资源
  async function fastFailExample() {
    const startTime = Date.now()

    const promises = [
      new Promise(resolve => setTimeout(() => resolve('快速成功'), 100)),
      new Promise((_, reject) => setTimeout(() => reject('快速失败'), 200)),
      new Promise(resolve => setTimeout(() => resolve('慢速成功'), 2000)), // 不会等待
    ]

    try {
      await Promise.all(promises)
    } catch (error) {
      const endTime = Date.now()
      console.log(`Promise.all 失败用时: ${endTime - startTime}ms`) // 约200ms
      console.log('第三个Promise可能仍在执行，但结果被忽略')
    }
  }

  // Promise.allSettled() - 等待全部完成
  async function waitAllExample() {
    const startTime = Date.now()

    const promises = [
      new Promise(resolve => setTimeout(() => resolve('快速成功'), 100)),
      new Promise((_, reject) => setTimeout(() => reject('快速失败'), 200)),
      new Promise(resolve => setTimeout(() => resolve('慢速成功'), 2000)), // 会等待
    ]

    await Promise.allSettled(promises)
    const endTime = Date.now()
    console.log(`Promise.allSettled 完成用时: ${endTime - startTime}ms`) // 约2000ms
  }
}

// 5. 实用工具函数
function createPromiseUtils() {
  // 将Promise.all()行为改为类似allSettled()
  async function allWithDetails(promises) {
    try {
      const results = await Promise.all(promises)
      return {
        success: true,
        results: results.map(value => ({ status: 'fulfilled', value })),
      }
    } catch (error) {
      // 无法获取部分成功的结果
      return {
        success: false,
        error,
        results: [],
      }
    }
  }

  // 将Promise.allSettled()行为改为类似all()
  async function allSettledWithThrow(promises) {
    const results = await Promise.allSettled(promises)

    const firstRejected = results.find(result => result.status === 'rejected')
    if (firstRejected) {
      throw firstRejected.reason
    }

    return results.map(result => result.value)
  }

  // 部分成功也算成功的版本
  async function allWithPartialSuccess(promises, minSuccessCount = 1) {
    const results = await Promise.allSettled(promises)

    const successful = results.filter(result => result.status === 'fulfilled')

    if (successful.length >= minSuccessCount) {
      return {
        success: true,
        data: successful.map(result => result.value),
        failed: results.filter(result => result.status === 'rejected').length,
      }
    } else {
      throw new Error(`至少需要${minSuccessCount}个成功，实际成功${successful.length}个`)
    }
  }

  return {
    allWithDetails,
    allSettledWithThrow,
    allWithPartialSuccess,
  }
}

// 辅助函数
function fetchData(endpoint) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (Math.random() > 0.3) {
        resolve(`${endpoint} 数据`)
      } else {
        reject(new Error(`${endpoint} 请求失败`))
      }
    }, Math.random() * 1000)
  })
}

function fetchUser(id) {
  return new Promise(resolve => {
    setTimeout(() => resolve({ id, name: `User${id}` }), 100)
  })
}

function fetchUserProfile(id) {
  return new Promise(resolve => {
    setTimeout(() => resolve({ userId: id, bio: 'User bio' }), 150)
  })
}

function fetchUserSettings(id) {
  return new Promise(resolve => {
    setTimeout(() => resolve({ userId: id, theme: 'dark' }), 120)
  })
}

function syncUserPosts(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      Math.random() > 0.2 ? resolve(`Posts synced for user ${id}`) : reject('Posts sync failed')
    }, 200)
  })
}

function syncUserPhotos(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      Math.random() > 0.2 ? resolve(`Photos synced for user ${id}`) : reject('Photos sync failed')
    }, 300)
  })
}

function syncUserContacts(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      Math.random() > 0.2
        ? resolve(`Contacts synced for user ${id}`)
        : reject('Contacts sync failed')
    }, 250)
  })
}

function syncUserSettings(id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      Math.random() > 0.2
        ? resolve(`Settings synced for user ${id}`)
        : reject('Settings sync failed')
    }, 180)
  })
}
```

**使用场景选择指南：**

1. **Promise.all()**: 全部依赖场景，如页面初始化数据加载
2. **Promise.allSettled()**: 批量操作场景，如数据同步、批量更新

**记忆要点总结：**

- Promise.all()：快速失败，返回成功值数组，适合全部依赖
- Promise.allSettled()：等待全部完成，返回状态对象数组，适合批量处理
- all()节省资源但信息有限，allSettled()消耗更多资源但信息完整
- 根据业务需求选择：是否需要部分成功的结果
- 错误处理策略不同：中止 vs 继续

# **124. [高级]** Promise.race()的使用场景

- 用于包装请求函数中的超时操作。可以在Promise.all 中包含一个异步请求的同时可以再加入一个Promise.race 包含的超时操作

## 深度分析与补充

**问题本质解读：** 这道题考察Promise.race()的实际应用场景，面试官想了解你是否能在实际项目中合理运用竞速机制解决问题。

**知识点系统梳理：**

**Promise.race()的核心特性：**

- 返回第一个settled（fulfilled或rejected）的Promise结果
- 其他Promise继续执行但结果被忽略
- 适用于需要"最快响应"的场景

**实战应用举例：**

```javascript
// 1. 请求超时控制（最常见场景）
function withTimeout(promise, timeout, timeoutMessage = '请求超时') {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(timeoutMessage)), timeout)
  })

  return Promise.race([promise, timeoutPromise])
}

// 使用示例
async function fetchWithTimeout() {
  try {
    const data = await withTimeout(
      fetch('/api/slow-endpoint').then(r => r.json()),
      5000,
      '请求超时，请稍后重试',
    )
    console.log('请求成功:', data)
  } catch (error) {
    console.log('请求失败:', error.message)
  }
}

// 2. 多服务器竞速请求
async function fastestServerRequest(endpoint) {
  const servers = [
    'https://api1.example.com',
    'https://api2.example.com',
    'https://api3.example.com',
  ]

  const requests = servers.map(server =>
    fetch(`${server}${endpoint}`).then(response => ({
      server,
      data: response.json(),
    })),
  )

  try {
    const fastest = await Promise.race(requests)
    console.log(`最快响应来自: ${fastest.server}`)
    return fastest.data
  } catch (error) {
    console.log('所有服务器请求失败')
    throw error
  }
}

// 3. 用户交互竞速
function waitForUserAction(timeout = 10000) {
  const userClick = new Promise(resolve => {
    document.addEventListener('click', () => resolve('用户点击'), { once: true })
  })

  const userKeypress = new Promise(resolve => {
    document.addEventListener('keypress', () => resolve('用户按键'), { once: true })
  })

  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('用户无响应')), timeout)
  })

  return Promise.race([userClick, userKeypress, timeoutPromise])
}
```

**记忆要点总结：**

- 超时控制：防止请求无限等待
- 多源竞速：选择最快的数据源
- 用户交互：等待用户操作但设置超时
- 性能优化：选择最快的计算结果

# **125. [中级]** 如何取消一个Promise？

无法取消promise

## 深度分析与补充

**问题本质解读：** 这道题考察Promise的限制和替代方案，面试官想了解你是否理解Promise的设计原理和如何实现类似取消的效果。

**技术错误纠正：**

- Promise本身确实无法取消，但可以通过其他方式实现类似效果

**知识点系统梳理：**

**Promise无法取消的原因：**

- Promise代表一个已经开始的异步操作
- 状态一旦改变就不可逆
- 设计上追求简单性和可预测性

**替代方案：**

```javascript
// 1. 使用AbortController（现代方案）
function cancellableRequest(url) {
  const controller = new AbortController()

  const promise = fetch(url, {
    signal: controller.signal,
  }).then(response => response.json())

  return {
    promise,
    cancel: () => controller.abort(),
  }
}

// 2. 手动实现取消机制
function createCancellablePromise(executor) {
  let isCancelled = false

  const promise = new Promise((resolve, reject) => {
    executor(
      value => {
        if (!isCancelled) resolve(value)
      },
      reason => {
        if (!isCancelled) reject(reason)
      },
    )
  })

  return {
    promise,
    cancel: () => {
      isCancelled = true
    },
  }
}

// 3. 使用竞速机制模拟取消
function raceWithCancel(promise) {
  let cancelReject

  const cancelPromise = new Promise((_, reject) => {
    cancelReject = reject
  })

  const racePromise = Promise.race([promise, cancelPromise])

  return {
    promise: racePromise,
    cancel: () => cancelReject(new Error('操作已取消')),
  }
}
```

**记忆要点总结：**

- Promise本身不支持取消
- 使用AbortController处理网络请求取消
- 通过标志位和竞速机制实现类似效果
- 现代API设计中考虑取消机制

# **126. [高级]** 如何实现一个简单的Promise？

```javascript
const FULFILLED = 'fulfilled'
const REJECTED = 'rejected'
const PENDING = 'pending'

function MyPromise(exectue) {
  this.state = PENDING
  this.reason = null
  this.value = null

  this.onFulfilledCallbacks = []
  this.onRejectedCallbacks = []

  const that = this
  function resolve(value) {
    if (this.state === PENDING) {
      this.value = value
      this.state = FULFILLED
      that.onFulfilledCallbacks.forEach(fn => fn(that.value))
    }
  }

  function reject(reason) {
    if (this.state === PENDING) {
      this.reason = resaon
      this.state = REJECTED
      that.onRejectedCallbacks.forEach(fn => fn(that.resaon))
    }
  }

  try {
    exectue(resolve, reject)
  } catch (err) {
    reject(err)
  }
}
MyPromise.prototype.then = function (onFullfilled, onRejected) {
  if (typeof onFullfilled !== 'function') {
    onFullfilled = function (value) {
      return value
    }
  }

  if (typeof onRejected !== 'function') {
    onRejected = function (reason) {
      throw reason
    }
  }

  const that = this

  if (this.state === FULFILLED) {
    const promise2 = new MyPromiose((resolve, reject) => {
      setTimeout(() => {
        try {
          if (typeof onFullfilled !== 'function') {
            resolve(that.value)
          } else {
            const x = onFullfilled(that.value)
            resoveMyPromise(promise2, x, resolve, reject)
          }
        } catch (err) {
          reject(err)
        }
      }, 0)
    })
    return promise2
  }

  if (this.state === REJECTED) {
    const promise2 = new MyPromiose((resolve, reject) => {
      setTimeout(() => {
        try {
          if (typeof onRejected !== 'function') {
            reject(that.reason)
          } else {
            const x = onRejected(that.reason)
            resoveMyPromise(promise2, x, resolve, reject)
          }
        } catch (err) {
          reject(err)
        }
      }, 0)
    })
    return promise2
  }

  if (this.state === PENDING) {
    const promise2 = new MyPromise((resolve, reject) => {
      this.onFulfilledCallbacks.push(() => {
        setTimeout(() => {
          try {
            if (typeof onFullfilled !== 'function') {
              resolve(that.value)
            } else {
              const x = onFullfilled(that.value)
              resoveMyPromise(promise2, x, resolve, reject)
            }
          } catch (err) {
            reject(err)
          }
        }, 0)
      })
      this.onRejectedCallbacks.push(() => {
        setTimeout(() => {
          try {
            if (typeof onRejected !== 'function') {
              reject(that.reason)
            } else {
              const x = onRejected(that.reason)
              resoveMyPromise(promise2, x, resolve, reject)
            }
          } catch (err) {
            reject(err)
          }
        }, 0)
      })
    })
    return promise2
  }
}

MyPromise.prototype.catch = function (onRejected) {
  return this.then(null, onRejected)
}

MyPromise.prototype.finally = function (onFinally) {
  return this.then(onFinally, onFinally)
}

MyPromise.resolve = function (value) {
  if (value instanceof MyPromise) {
    return value
  }

  return new MyPromise((resolve, reject) => {
    resolve(value)
  })
}

MyPromise.reject = function (reason) {
  return new MyPromise((resolve, reject) => {
    reject(reason)
  })
}

MyPromise.prototype.all = function (promises = []) {
  return new MyPromise((resolve, reject) => {
    const result = []
    let count = 0

    if (promises.length === 0) {
      resolve(result)
    }

    for (const promise of promises) {
      MyPromise.resolve(promise).then(
        value => {
          count++
          result.push(value)
          if (count === promises.length) {
            resolve(reslut)
          }
        },
        reason => {
          reject(reason)
        },
      )
    }
  })
}

function resolveMyPromise(promise, x, resolve, reject) {
  if (x === promise) {
    return reject(new TypeError('promise and x is same value'))
  }

  if (x instanceof MyPromise) {
    x.then(y => {
      resolveMyPromise(promise, y, resolve, reject)
    }, reject)
  } else if (typeof x === 'object' || typeof x === 'function') {
    if (x === null) {
      return resolve(x)
    }

    try {
      var then = x.then
    } catch (err) {
      reject(err)
    }

    if (typeof then === 'function') {
      let called = false

      try {
        then.call(
          x,
          y => {
            if (called) return
            called = true
            resolveMyPromise(promise, y, resolve, reject)
          },
          r => {
            if (called) return
            called = true
            reject(r)
          },
        )
      } catch (e) {
        if (called) return
        reject(e)
      }
    } else {
      resolve(x)
    }
  } else {
    resolve(x)
  }
}
```

## 深度分析与补充

**问题本质解读：** 这道题考察对Promise内部实现机制的深度理解，面试官想了解你是否掌握Promise的核心原理和状态管理。

**技术错误纠正：**

- `exectue`应该是`executor`
- `resaon`应该是`reason`
- `resoveMyPromise`应该是`resolveMyPromise`
- `MyPromiose`应该是`MyPromise`
- `reslut`应该是`result`
- resolve和reject函数中的`this`指向问题需要修正

**完整的Promise实现：**

```javascript
const FULFILLED = 'fulfilled'
const REJECTED = 'rejected'
const PENDING = 'pending'

function MyPromise(executor) {
  this.state = PENDING
  this.value = null
  this.reason = null

  // 存储异步情况下的回调函数
  this.onFulfilledCallbacks = []
  this.onRejectedCallbacks = []

  const self = this

  function resolve(value) {
    if (self.state === PENDING) {
      self.state = FULFILLED
      self.value = value
      // 执行所有成功回调
      self.onFulfilledCallbacks.forEach(callback => callback())
    }
  }

  function reject(reason) {
    if (self.state === PENDING) {
      self.state = REJECTED
      self.reason = reason
      // 执行所有失败回调
      self.onRejectedCallbacks.forEach(callback => callback())
    }
  }

  try {
    executor(resolve, reject)
  } catch (error) {
    reject(error)
  }
}

// then方法实现
MyPromise.prototype.then = function (onFulfilled, onRejected) {
  // 参数校验，确保是函数
  onFulfilled = typeof onFulfilled === 'function' ? onFulfilled : value => value
  onRejected =
    typeof onRejected === 'function'
      ? onRejected
      : reason => {
          throw reason
        }

  const self = this

  // 返回新的Promise，支持链式调用
  const promise2 = new MyPromise((resolve, reject) => {
    if (self.state === FULFILLED) {
      // 异步执行，确保promise2已经创建
      setTimeout(() => {
        try {
          const x = onFulfilled(self.value)
          resolvePromise(promise2, x, resolve, reject)
        } catch (error) {
          reject(error)
        }
      }, 0)
    }

    if (self.state === REJECTED) {
      setTimeout(() => {
        try {
          const x = onRejected(self.reason)
          resolvePromise(promise2, x, resolve, reject)
        } catch (error) {
          reject(error)
        }
      }, 0)
    }

    if (self.state === PENDING) {
      // 异步情况，将回调存储起来
      self.onFulfilledCallbacks.push(() => {
        setTimeout(() => {
          try {
            const x = onFulfilled(self.value)
            resolvePromise(promise2, x, resolve, reject)
          } catch (error) {
            reject(error)
          }
        }, 0)
      })

      self.onRejectedCallbacks.push(() => {
        setTimeout(() => {
          try {
            const x = onRejected(self.reason)
            resolvePromise(promise2, x, resolve, reject)
          } catch (error) {
            reject(error)
          }
        }, 0)
      })
    }
  })

  return promise2
}

// 处理Promise解析过程
function resolvePromise(promise2, x, resolve, reject) {
  // 避免循环引用
  if (promise2 === x) {
    return reject(new TypeError('Chaining cycle detected for promise'))
  }

  let called = false

  if (x !== null && (typeof x === 'object' || typeof x === 'function')) {
    try {
      const then = x.then

      if (typeof then === 'function') {
        // x是thenable对象
        then.call(
          x,
          y => {
            if (called) return
            called = true
            resolvePromise(promise2, y, resolve, reject)
          },
          r => {
            if (called) return
            called = true
            reject(r)
          },
        )
      } else {
        // x是普通对象
        resolve(x)
      }
    } catch (error) {
      if (called) return
      called = true
      reject(error)
    }
  } else {
    // x是普通值
    resolve(x)
  }
}

// catch方法
MyPromise.prototype.catch = function (onRejected) {
  return this.then(null, onRejected)
}

// finally方法
MyPromise.prototype.finally = function (onFinally) {
  return this.then(
    value => MyPromise.resolve(onFinally()).then(() => value),
    reason =>
      MyPromise.resolve(onFinally()).then(() => {
        throw reason
      }),
  )
}

// 静态方法
MyPromise.resolve = function (value) {
  if (value instanceof MyPromise) {
    return value
  }
  return new MyPromise(resolve => resolve(value))
}

MyPromise.reject = function (reason) {
  return new MyPromise((_, reject) => reject(reason))
}

MyPromise.all = function (promises) {
  return new MyPromise((resolve, reject) => {
    if (!Array.isArray(promises)) {
      return reject(new TypeError('Argument must be an array'))
    }

    const results = []
    let completedCount = 0

    if (promises.length === 0) {
      return resolve(results)
    }

    promises.forEach((promise, index) => {
      MyPromise.resolve(promise).then(
        value => {
          results[index] = value
          completedCount++
          if (completedCount === promises.length) {
            resolve(results)
          }
        },
        reason => reject(reason),
      )
    })
  })
}

MyPromise.race = function (promises) {
  return new MyPromise((resolve, reject) => {
    if (!Array.isArray(promises)) {
      return reject(new TypeError('Argument must be an array'))
    }

    promises.forEach(promise => {
      MyPromise.resolve(promise).then(resolve, reject)
    })
  })
}
```

**实现要点总结：**

- 状态管理：pending → fulfilled/rejected
- 回调队列：处理异步情况
- then方法：返回新Promise，支持链式调用
- 值处理：普通值、Promise、thenable对象
- 错误处理：try-catch和reject机制
- 静态方法：resolve、reject、all、race

# **127. [中级]** Promise中的错误传播机制

.catch()

## 深度分析与补充

**问题本质解读：** 这道题考察Promise错误传播的机制，面试官想了解你是否理解Promise链中错误如何传播和处理。

**知识点系统梳理：**

**Promise错误传播的核心机制：**

1. **错误冒泡**：错误会沿着Promise链向下传播
2. **跳过成功回调**：错误状态会跳过then的成功回调
3. **catch捕获**：catch可以捕获之前链条中的任何错误
4. **错误恢复**：catch处理后可以返回正常值，恢复Promise链

**实战应用举例：**

```javascript
// 1. 基础错误传播
Promise.resolve('开始')
  .then(value => {
    console.log('步骤1:', value)
    throw new Error('步骤1出错')
  })
  .then(value => {
    console.log('步骤2: 不会执行') // 跳过
    return value + ' -> 步骤2'
  })
  .catch(error => {
    console.log('捕获错误:', error.message) // "步骤1出错"
    return '错误已处理' // 恢复正常流程
  })
  .then(value => {
    console.log('恢复执行:', value) // "错误已处理"
  })

// 2. 错误类型和处理策略
function handleDifferentErrors() {
  return Promise.resolve()
    .then(() => {
      const errorType = Math.floor(Math.random() * 3)
      switch (errorType) {
        case 0:
          throw new TypeError('类型错误')
        case 1:
          throw new ReferenceError('引用错误')
        case 2:
          throw new Error('普通错误')
      }
    })
    .catch(error => {
      if (error instanceof TypeError) {
        return '类型错误已修复'
      } else if (error instanceof ReferenceError) {
        return '引用错误已修复'
      } else {
        return '普通错误已修复'
      }
    })
}

// 3. 分层错误处理
function processUserData(userId) {
  return fetchUser(userId)
    .catch(error => {
      console.log('获取用户失败，使用默认用户')
      return { id: userId, name: 'Unknown User' }
    })
    .then(user => processUser(user))
    .catch(error => {
      console.log('处理用户失败，返回错误信息')
      return { error: true, message: error.message }
    })
}
```

**记忆要点总结：**

- 错误自动向下传播，跳过成功回调
- catch可以捕获之前链条中的任何错误
- catch处理后可以恢复正常流程
- 合理使用分层错误处理和重试机制

### async/await（8道）

# **128. [初级]** async/await的基本用法

```javascript
async function(){
  await fetch('/get/github/user=pan').then((response)=>response.json())
}
```

## 深度分析与补充

**问题本质解读：** 这道题考察async/await的基础语法和使用方式，面试官想了解你是否掌握现代JavaScript异步编程的核心语法。

**技术错误纠正：**

- 函数缺少函数名
- 可以直接await fetch的结果，不需要额外的then
- 应该有返回值或错误处理

**知识点系统梳理：**

**async/await基础语法：**

1. **async函数**：声明异步函数，自动返回Promise
2. **await表达式**：等待Promise完成，获取resolved值
3. **错误处理**：使用try-catch捕获异步异常
4. **返回值**：async函数总是返回Promise

**实战应用举例：**

```javascript
// 1. 基础用法示例
async function fetchUserData() {
  try {
    // await等待Promise完成
    const response = await fetch('/api/user/123')

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 解析JSON数据
    const userData = await response.json()

    console.log('用户数据:', userData)
    return userData
  } catch (error) {
    console.error('获取用户数据失败:', error)
    throw error // 重新抛出错误
  }
}

// 2. 多个异步操作
async function getUserProfile(userId) {
  try {
    // 串行执行
    const user = await fetchUser(userId)
    const posts = await fetchUserPosts(user.id)
    const friends = await fetchUserFriends(user.id)

    return {
      user,
      posts,
      friends,
      loadTime: new Date().toISOString(),
    }
  } catch (error) {
    console.error('获取用户资料失败:', error)
    return null
  }
}

// 3. 并行执行多个异步操作
async function getUserProfileParallel(userId) {
  try {
    // 并行执行，提高性能
    const [user, posts, friends] = await Promise.all([
      fetchUser(userId),
      fetchUserPosts(userId),
      fetchUserFriends(userId),
    ])

    return { user, posts, friends }
  } catch (error) {
    console.error('获取用户资料失败:', error)
    return null
  }
}

// 4. 条件异步操作
async function processUserData(userId, includeDetails = false) {
  const user = await fetchUser(userId)

  if (includeDetails) {
    // 条件性执行额外的异步操作
    user.details = await fetchUserDetails(user.id)
    user.preferences = await fetchUserPreferences(user.id)
  }

  return user
}

// 5. 异步迭代
async function processMultipleUsers(userIds) {
  const results = []

  for (const userId of userIds) {
    try {
      const user = await fetchUser(userId)
      results.push(user)
    } catch (error) {
      console.error(`处理用户${userId}失败:`, error)
      results.push(null)
    }
  }

  return results
}

// 6. 使用async/await的工具函数
async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

async function retryOperation(operation, maxRetries = 3, delayMs = 1000) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation()
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error // 最后一次重试失败，抛出错误
      }

      console.log(`操作失败，${delayMs}ms后重试 (${i + 1}/${maxRetries})`)
      await delay(delayMs)
    }
  }
}

// 7. 复杂的异步流程控制
async function complexAsyncFlow() {
  try {
    // 步骤1：初始化
    console.log('开始复杂异步流程')
    const config = await loadConfiguration()

    // 步骤2：验证配置
    if (!validateConfig(config)) {
      throw new Error('配置验证失败')
    }

    // 步骤3：并行执行多个初始化任务
    const [database, cache, logger] = await Promise.all([
      initializeDatabase(config.db),
      initializeCache(config.cache),
      initializeLogger(config.logging),
    ])

    // 步骤4：依次执行依赖任务
    const services = await initializeServices(database, cache)
    const routes = await setupRoutes(services)
    const server = await startServer(routes, config.port)

    console.log('复杂异步流程完成')
    return {
      server,
      services,
      config,
    }
  } catch (error) {
    console.error('复杂异步流程失败:', error)
    throw error
  }
}

// 辅助函数
async function fetchUser(id) {
  const response = await fetch(`/api/users/${id}`)
  if (!response.ok) throw new Error('用户不存在')
  return response.json()
}

async function fetchUserPosts(id) {
  const response = await fetch(`/api/users/${id}/posts`)
  return response.json()
}

async function fetchUserFriends(id) {
  const response = await fetch(`/api/users/${id}/friends`)
  return response.json()
}
```

**async/await的核心特性：**

1. **同步风格**：以同步代码的方式编写异步逻辑
2. **错误处理**：使用try-catch统一处理异步错误
3. **自动Promise包装**：async函数自动返回Promise
4. **顺序执行**：await会暂停函数执行，等待Promise完成

**常见使用模式：**

```javascript
// 模式1：基础异步函数
async function basicAsync() {
  const result = await someAsyncOperation()
  return result
}

// 模式2：错误处理
async function withErrorHandling() {
  try {
    const result = await riskyOperation()
    return result
  } catch (error) {
    console.error('操作失败:', error)
    return null
  }
}

// 模式3：并行执行
async function parallelExecution() {
  const [result1, result2] = await Promise.all([operation1(), operation2()])
  return { result1, result2 }
}

// 模式4：条件异步
async function conditionalAsync(condition) {
  const baseData = await getBaseData()

  if (condition) {
    baseData.extra = await getExtraData()
  }

  return baseData
}
```

**记忆要点总结：**

- async声明异步函数，await等待Promise完成
- 以同步风格编写异步代码，提高可读性
- 使用try-catch处理异步错误
- async函数自动返回Promise
- 合理使用并行和串行执行优化性能

# **129. [中级]** async函数返回什么？

- 返回一个promise

## 深度分析与补充

**问题本质解读：** 这道题考察async函数的返回值机制，面试官想了解你是否理解async函数与Promise的关系和自动包装机制。

**知识点系统梳理：**

**async函数返回值的处理规则：**

1. **自动Promise包装**：无论返回什么，都会被包装成Promise
2. **返回普通值**：Promise.resolve(value)
3. **返回Promise**：直接返回该Promise
4. **抛出异常**：Promise.reject(error)
5. **无返回值**：Promise.resolve(undefined)

**实战应用举例：**

```javascript
// 1. 返回普通值
async function returnValue() {
  return 'hello world'
}
// 等价于：Promise.resolve('hello world')

returnValue().then(value => {
  console.log(value) // "hello world"
})

// 2. 返回Promise
async function returnPromise() {
  return Promise.resolve('from promise')
}
// 直接返回Promise，不会双重包装

returnPromise().then(value => {
  console.log(value) // "from promise"
})

// 3. 抛出异常
async function throwError() {
  throw new Error('something went wrong')
}
// 等价于：Promise.reject(new Error('something went wrong'))

throwError().catch(error => {
  console.log(error.message) // "something went wrong"
})

// 4. 无返回值
async function noReturn() {
  console.log('执行一些操作')
  // 没有return语句
}
// 等价于：Promise.resolve(undefined)

noReturn().then(value => {
  console.log(value) // undefined
})

// 5. 复杂返回值处理
async function complexReturn(type) {
  switch (type) {
    case 'object':
      return { data: 'object data', timestamp: Date.now() }
    case 'array':
      return [1, 2, 3, 4, 5]
    case 'promise':
      return fetch('/api/data').then(r => r.json())
    case 'async':
      const result = await someAsyncOperation()
      return result
    case 'error':
      throw new Error('Intentional error')
    default:
      return null
  }
}

// 使用示例
async function demonstrateReturns() {
  try {
    const obj = await complexReturn('object')
    console.log('对象:', obj)

    const arr = await complexReturn('array')
    console.log('数组:', arr)

    const promiseResult = await complexReturn('promise')
    console.log('Promise结果:', promiseResult)

    const asyncResult = await complexReturn('async')
    console.log('异步结果:', asyncResult)
  } catch (error) {
    console.log('捕获错误:', error.message)
  }
}
```

**async函数返回值的特殊情况：**

```javascript
// 1. 返回thenable对象
async function returnThenable() {
  return {
    then(resolve, reject) {
      setTimeout(() => resolve('thenable result'), 1000)
    },
  }
}

// 2. 返回另一个async函数的调用
async function innerAsync() {
  return 'inner result'
}

async function outerAsync() {
  return innerAsync() // 返回Promise
}

// 3. 条件返回
async function conditionalReturn(condition) {
  if (condition) {
    return await fetchData()
  } else {
    return 'default value'
  }
}

// 4. 在try-catch中的返回
async function returnInTryCatch() {
  try {
    const data = await riskyOperation()
    return data
  } catch (error) {
    console.error('操作失败:', error)
    return null // 返回默认值
  }
}
```

**与Promise的对比：**

```javascript
// Promise写法
function promiseFunction() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve('promise result')
    }, 1000)
  })
}

// async/await写法
async function asyncFunction() {
  await new Promise(resolve => setTimeout(resolve, 1000))
  return 'async result'
}

// 两者都返回Promise
console.log(promiseFunction()) // Promise对象
console.log(asyncFunction()) // Promise对象
```

**记忆要点总结：**

- async函数总是返回Promise对象
- 返回值自动被Promise.resolve()包装
- 抛出异常会变成Promise.reject()
- 无返回值等于返回Promise.resolve(undefined)
- 返回Promise不会双重包装

# **130. [中级]** 如何在async函数中处理错误？

- 在外部包一个try-catch 捕获异常

## 深度分析与补充

**问题本质解读：** 这道题考察async/await中的错误处理机制，面试官想了解你是否掌握异步错误处理的最佳实践。

**技术错误纠正：**

- 不是"在外部包一个try-catch"，而是在async函数内部使用try-catch
- 还有其他多种错误处理方式

**知识点系统梳理：**

**async/await错误处理的多种方式：**

1. **try-catch块**：在async函数内部捕获错误
2. **Promise.catch()**：链式调用处理错误
3. **混合方式**：结合try-catch和.catch()
4. **全局错误处理**：unhandledRejection事件

**实战应用举例：**

```javascript
// 1. 基础try-catch错误处理
async function basicErrorHandling() {
  try {
    const response = await fetch('/api/data')

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('请求失败:', error.message)
    throw error // 重新抛出或返回默认值
  }
}

// 2. 多层错误处理
async function multiLevelErrorHandling(userId) {
  try {
    // 第一层：获取用户基本信息
    const user = await fetchUser(userId)

    try {
      // 第二层：获取用户详细信息
      const profile = await fetchUserProfile(user.id)
      user.profile = profile
    } catch (profileError) {
      console.warn('获取用户资料失败，使用默认资料:', profileError.message)
      user.profile = getDefaultProfile()
    }

    try {
      // 第三层：获取用户设置
      const settings = await fetchUserSettings(user.id)
      user.settings = settings
    } catch (settingsError) {
      console.warn('获取用户设置失败，使用默认设置:', settingsError.message)
      user.settings = getDefaultSettings()
    }

    return user
  } catch (error) {
    console.error('获取用户信息失败:', error.message)
    return null
  }
}

// 3. 条件错误处理
async function conditionalErrorHandling(operation, fallbackOperation) {
  try {
    return await operation()
  } catch (error) {
    console.log('主操作失败，尝试备用操作:', error.message)

    if (fallbackOperation) {
      try {
        return await fallbackOperation()
      } catch (fallbackError) {
        console.error('备用操作也失败:', fallbackError.message)
        throw new Error('所有操作都失败了')
      }
    } else {
      throw error
    }
  }
}

// 4. 批量操作的错误处理
async function batchOperationWithErrorHandling(items) {
  const results = []
  const errors = []

  for (const item of items) {
    try {
      const result = await processItem(item)
      results.push({ item, result, status: 'success' })
    } catch (error) {
      errors.push({ item, error: error.message, status: 'failed' })
      console.error(`处理项目${item.id}失败:`, error.message)
    }
  }

  return {
    results,
    errors,
    summary: {
      total: items.length,
      successful: results.length,
      failed: errors.length,
    },
  }
}

// 5. 超时和重试的错误处理
async function withTimeoutAndRetry(operation, timeout = 5000, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // 添加超时控制
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('操作超时')), timeout)
      })

      const result = await Promise.race([operation(), timeoutPromise])
      return result
    } catch (error) {
      console.log(`第${attempt}次尝试失败:`, error.message)

      if (attempt === maxRetries) {
        throw new Error(`操作失败，已重试${maxRetries}次: ${error.message}`)
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
    }
  }
}

// 6. 使用Promise.catch()的错误处理
async function promiseCatchErrorHandling() {
  const userData = await fetchUser(123).catch(error => {
    console.error('获取用户失败:', error)
    return getDefaultUser() // 返回默认用户
  })

  const userPosts = await fetchUserPosts(userData.id).catch(error => {
    console.error('获取用户文章失败:', error)
    return [] // 返回空数组
  })

  return {
    user: userData,
    posts: userPosts,
  }
}

// 7. 混合错误处理策略
async function hybridErrorHandling(userId) {
  try {
    // 关键操作使用try-catch
    const user = await fetchUser(userId)

    // 可选操作使用.catch()
    const [posts, friends, settings] = await Promise.all([
      fetchUserPosts(userId).catch(() => []),
      fetchUserFriends(userId).catch(() => []),
      fetchUserSettings(userId).catch(() => getDefaultSettings()),
    ])

    return {
      user,
      posts,
      friends,
      settings,
      loadedAt: new Date().toISOString(),
    }
  } catch (error) {
    console.error('加载用户数据失败:', error)
    throw new Error(`无法加载用户${userId}的数据: ${error.message}`)
  }
}

// 8. 错误分类处理
async function categorizedErrorHandling() {
  try {
    const data = await fetchSensitiveData()
    return data
  } catch (error) {
    if (error.name === 'NetworkError') {
      console.error('网络错误，请检查网络连接')
      throw new Error('网络连接失败，请稍后重试')
    } else if (error.status === 401) {
      console.error('认证失败，需要重新登录')
      throw new Error('认证失败，请重新登录')
    } else if (error.status === 403) {
      console.error('权限不足')
      throw new Error('您没有权限访问此资源')
    } else if (error.status >= 500) {
      console.error('服务器错误')
      throw new Error('服务器暂时不可用，请稍后重试')
    } else {
      console.error('未知错误:', error)
      throw new Error('操作失败，请联系技术支持')
    }
  }
}

// 9. 资源清理的错误处理
async function resourceCleanupErrorHandling() {
  let connection = null
  let transaction = null

  try {
    connection = await createDatabaseConnection()
    transaction = await connection.beginTransaction()

    const result = await performDatabaseOperations(transaction)
    await transaction.commit()

    return result
  } catch (error) {
    console.error('数据库操作失败:', error)

    if (transaction) {
      try {
        await transaction.rollback()
      } catch (rollbackError) {
        console.error('回滚失败:', rollbackError)
      }
    }

    throw error
  } finally {
    // 确保资源被清理
    if (connection) {
      try {
        await connection.close()
      } catch (closeError) {
        console.error('关闭连接失败:', closeError)
      }
    }
  }
}
```

**错误处理最佳实践：**

1. **明确错误边界**：在合适的层级处理错误
2. **提供有意义的错误信息**：帮助调试和用户理解
3. **优雅降级**：提供默认值或备用方案
4. **资源清理**：使用finally确保资源释放
5. **错误分类**：根据错误类型采取不同策略

**记忆要点总结：**

- 在async函数内部使用try-catch捕获await的错误
- 可以结合Promise.catch()进行链式错误处理
- 支持多层错误处理和条件错误处理
- 使用finally进行资源清理
- 根据错误类型制定不同的处理策略

# **131. [中级]** async/await相比Promise的优势

- async await 本质上是 generator和promise 结合的语法糖，使用更加简洁，心智模型更加简单，以同步（类似）的方式来执行异步操作，可维护性更强

## 深度分析与补充

**问题本质解读：** 这道题考察async/await与Promise的对比，面试官想了解你是否理解两种异步编程方式的差异和各自的优势。

**知识点系统梳理：**

**async/await相比Promise的核心优势：**

1. **代码可读性**：同步风格的异步代码，更易理解
2. **错误处理**：统一的try-catch错误处理机制
3. **调试友好**：更好的堆栈跟踪和断点调试
4. **条件逻辑**：更容易处理复杂的条件异步逻辑
5. **中间值处理**：避免Promise链中的中间值传递问题

**实战应用举例：**

**通用JavaScript示例：**

```javascript
// 1. 代码可读性对比
// Promise链式调用
function fetchUserDataPromise(userId) {
  return fetchUser(userId)
    .then(user => {
      return fetchUserProfile(user.id).then(profile => {
        return fetchUserSettings(user.id).then(settings => {
          return {
            user,
            profile,
            settings,
            timestamp: Date.now(),
          }
        })
      })
    })
    .catch(error => {
      console.error('获取用户数据失败:', error)
      throw error
    })
}

// async/await写法
async function fetchUserDataAsync(userId) {
  try {
    const user = await fetchUser(userId)
    const profile = await fetchUserProfile(user.id)
    const settings = await fetchUserSettings(user.id)

    return {
      user,
      profile,
      settings,
      timestamp: Date.now(),
    }
  } catch (error) {
    console.error('获取用户数据失败:', error)
    throw error
  }
}

// 2. 错误处理对比
// Promise错误处理
function processDataPromise(data) {
  return validateData(data)
    .then(validData => {
      return transformData(validData)
    })
    .then(transformedData => {
      return saveData(transformedData)
    })
    .catch(validationError => {
      if (validationError.type === 'VALIDATION_ERROR') {
        console.error('数据验证失败:', validationError)
        return getDefaultData()
      }
      throw validationError
    })
    .catch(transformError => {
      if (transformError.type === 'TRANSFORM_ERROR') {
        console.error('数据转换失败:', transformError)
        return getRawData()
      }
      throw transformError
    })
    .catch(saveError => {
      console.error('数据保存失败:', saveError)
      throw saveError
    })
}

// async/await错误处理
async function processDataAsync(data) {
  try {
    const validData = await validateData(data)
    const transformedData = await transformData(validData)
    const result = await saveData(transformedData)
    return result
  } catch (error) {
    if (error.type === 'VALIDATION_ERROR') {
      console.error('数据验证失败:', error)
      return getDefaultData()
    } else if (error.type === 'TRANSFORM_ERROR') {
      console.error('数据转换失败:', error)
      return getRawData()
    } else {
      console.error('数据保存失败:', error)
      throw error
    }
  }
}

// 3. 条件逻辑处理对比
// Promise条件逻辑
function conditionalProcessPromise(userId, includeDetails) {
  return fetchUser(userId)
    .then(user => {
      if (includeDetails) {
        return fetchUserDetails(user.id).then(details => {
          user.details = details
          return user
        })
      }
      return user
    })
    .then(user => {
      if (user.isPremium) {
        return fetchPremiumFeatures(user.id).then(features => {
          user.premiumFeatures = features
          return user
        })
      }
      return user
    })
}

// async/await条件逻辑
async function conditionalProcessAsync(userId, includeDetails) {
  const user = await fetchUser(userId)

  if (includeDetails) {
    user.details = await fetchUserDetails(user.id)
  }

  if (user.isPremium) {
    user.premiumFeatures = await fetchPremiumFeatures(user.id)
  }

  return user
}
```

**Vue 3框架应用示例：**

```vue
<template>
  <div class="data-processor">
    <button
      @click="processWithPromise"
      :disabled="processing"
    >
      Promise方式处理
    </button>
    <button
      @click="processWithAsync"
      :disabled="processing"
    >
      async/await方式处理
    </button>

    <div
      v-if="processing"
      class="loading"
    >
      处理中...
    </div>
    <div
      v-if="result"
      class="result"
    >
      <h3>处理结果:</h3>
      <pre>{{ JSON.stringify(result, null, 2) }}</pre>
    </div>
    <div
      v-if="error"
      class="error"
    >
      错误: {{ error }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const processing = ref(false)
const result = ref(null)
const error = ref(null)

// Promise方式的数据处理
const processWithPromise = () => {
  processing.value = true
  error.value = null

  fetchUserAPI(123)
    .then(user => {
      return Promise.all([user, fetchUserPostsAPI(user.id), fetchUserStatsAPI(user.id)])
    })
    .then(([user, posts, stats]) => {
      return processUserDataAPI({
        user,
        posts,
        stats,
      })
    })
    .then(processedData => {
      result.value = processedData
    })
    .catch(err => {
      error.value = err.message
    })
    .finally(() => {
      processing.value = false
    })
}

// async/await方式的数据处理
const processWithAsync = async () => {
  processing.value = true
  error.value = null

  try {
    const user = await fetchUserAPI(123)
    const [posts, stats] = await Promise.all([
      fetchUserPostsAPI(user.id),
      fetchUserStatsAPI(user.id),
    ])

    const processedData = await processUserDataAPI({
      user,
      posts,
      stats,
    })

    result.value = processedData
  } catch (err) {
    error.value = err.message
  } finally {
    processing.value = false
  }
}

// 组合式函数：展示async/await的优势
const useAsyncDataProcessor = () => {
  const data = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const processData = async config => {
    loading.value = true
    error.value = null

    try {
      // 复杂的条件逻辑用async/await更清晰
      let result = await fetchInitialData(config.source)

      if (config.transform) {
        result = await transformData(result, config.transformOptions)
      }

      if (config.validate) {
        const isValid = await validateData(result)
        if (!isValid) {
          throw new Error('数据验证失败')
        }
      }

      if (config.enrich) {
        const enrichedData = await enrichData(result)
        result = { ...result, ...enrichedData }
      }

      data.value = result
      return result
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  return { data, loading, error, processData }
}
</script>
```

**优势对比总结：**

| 特性       | Promise             | async/await        |
| ---------- | ------------------- | ------------------ |
| 代码可读性 | 链式调用，嵌套复杂  | 同步风格，线性阅读 |
| 错误处理   | 多个catch，分散处理 | 统一try-catch      |
| 调试体验   | 堆栈跟踪困难        | 清晰的堆栈信息     |
| 条件逻辑   | 嵌套复杂            | 自然的if/else      |
| 中间值     | 需要传递或嵌套      | 直接使用变量       |
| 学习成本   | 需要理解链式概念    | 接近同步代码思维   |

**记忆要点总结：**

- async/await是Promise的语法糖，提供更好的开发体验
- 代码可读性和维护性显著提升
- 错误处理更加统一和直观
- 调试和堆栈跟踪更加友好
- 复杂条件逻辑处理更加自然
- 本质上仍然是Promise，性能相当

# **132. [中级]** 如何并发执行多个async操作？

- Promise.all
- Promise.race

## 深度分析与补充

**问题本质解读：** 这道题考察async/await中的并发处理，面试官想了解你是否理解如何在async函数中实现真正的并发执行。

**知识点系统梳理：**

**async/await并发执行的核心方法：**

1. **Promise.all()**：等待所有操作完成
2. **Promise.allSettled()**：等待所有操作结束（不管成功失败）
3. **Promise.race()**：返回最快完成的操作
4. **Promise.any()**：返回最快成功的操作
5. **手动并发控制**：控制并发数量

**实战应用举例：**

**通用JavaScript示例：**

```javascript
// 1. 基础并发执行
// ❌ 串行执行（慢）
async function serialExecution() {
  const start = Date.now()

  const user = await fetchUser(1) // 1秒
  const posts = await fetchPosts(1) // 1秒
  const comments = await fetchComments(1) // 1秒

  console.log(`串行执行耗时: ${Date.now() - start}ms`) // 约3000ms
  return { user, posts, comments }
}

// ✅ 并发执行（快）
async function concurrentExecution() {
  const start = Date.now()

  const [user, posts, comments] = await Promise.all([
    fetchUser(1), // 并发执行
    fetchPosts(1), // 并发执行
    fetchComments(1), // 并发执行
  ])

  console.log(`并发执行耗时: ${Date.now() - start}ms`) // 约1000ms
  return { user, posts, comments }
}

// 2. 混合串行和并发
async function hybridExecution(userId) {
  // 第一步：获取用户信息（必须先执行）
  const user = await fetchUser(userId)

  // 第二步：基于用户信息并发获取相关数据
  const [profile, posts, friends, settings] = await Promise.all([
    fetchUserProfile(user.id),
    fetchUserPosts(user.id),
    fetchUserFriends(user.id),
    fetchUserSettings(user.id),
  ])

  // 第三步：基于前面的数据进行后续处理
  const [analytics, recommendations] = await Promise.all([
    generateAnalytics(user, posts),
    generateRecommendations(user, friends),
  ])

  return {
    user,
    profile,
    posts,
    friends,
    settings,
    analytics,
    recommendations,
  }
}

// 3. 错误处理的并发执行
async function concurrentWithErrorHandling() {
  try {
    const results = await Promise.allSettled([
      fetchCriticalData(),
      fetchOptionalData1(),
      fetchOptionalData2(),
      fetchOptionalData3(),
    ])

    const [critical, optional1, optional2, optional3] = results

    // 检查关键数据
    if (critical.status === 'rejected') {
      throw new Error('关键数据获取失败: ' + critical.reason.message)
    }

    // 处理可选数据
    const optionalData = [optional1, optional2, optional3]
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value)

    return {
      critical: critical.value,
      optional: optionalData,
      errors: results
        .filter(result => result.status === 'rejected')
        .map(result => result.reason.message),
    }
  } catch (error) {
    console.error('并发执行失败:', error)
    throw error
  }
}

// 4. 控制并发数量
async function limitedConcurrency(tasks, limit = 3) {
  const results = []

  for (let i = 0; i < tasks.length; i += limit) {
    const batch = tasks.slice(i, i + limit)
    const batchResults = await Promise.all(batch.map(task => task().catch(error => ({ error }))))
    results.push(...batchResults)
  }

  return results
}

// 使用示例
async function processManyItems(items) {
  const tasks = items.map(item => () => processItem(item))
  return await limitedConcurrency(tasks, 5) // 最多5个并发
}

// 5. 超时控制的并发执行
async function concurrentWithTimeout(operations, timeout = 5000) {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('操作超时')), timeout)
  })

  const operationsWithTimeout = operations.map(op => Promise.race([op, timeoutPromise]))

  return await Promise.allSettled(operationsWithTimeout)
}
```

**Vue 3框架应用示例：**

```vue
<template>
  <div class="concurrent-demo">
    <div class="controls">
      <button
        @click="loadDataSerial"
        :disabled="loading"
      >
        串行加载 (慢)
      </button>
      <button
        @click="loadDataConcurrent"
        :disabled="loading"
      >
        并发加载 (快)
      </button>
      <button
        @click="loadDataWithErrorHandling"
        :disabled="loading"
      >
        容错并发加载
      </button>
    </div>

    <div
      v-if="loading"
      class="loading"
    >
      加载中... {{ loadingProgress }}%
    </div>

    <div
      v-if="data"
      class="results"
    >
      <h3>加载结果 (耗时: {{ loadTime }}ms)</h3>
      <div class="data-section">
        <h4>用户信息</h4>
        <p>{{ data.user?.name || '加载失败' }}</p>
      </div>
      <div class="data-section">
        <h4>文章数量</h4>
        <p>{{ data.posts?.length || 0 }}</p>
      </div>
      <div class="data-section">
        <h4>好友数量</h4>
        <p>{{ data.friends?.length || 0 }}</p>
      </div>
    </div>

    <div
      v-if="errors.length > 0"
      class="errors"
    >
      <h4>错误信息:</h4>
      <ul>
        <li
          v-for="error in errors"
          :key="error"
        >
          {{ error }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const loading = ref(false)
const loadingProgress = ref(0)
const data = ref(null)
const errors = ref([])
const loadTime = ref(0)

// 串行加载数据
const loadDataSerial = async () => {
  const startTime = Date.now()
  loading.value = true
  loadingProgress.value = 0
  errors.value = []

  try {
    loadingProgress.value = 25
    const user = await fetchUserAPI(123)

    loadingProgress.value = 50
    const posts = await fetchUserPostsAPI(user.id)

    loadingProgress.value = 75
    const friends = await fetchUserFriendsAPI(user.id)

    loadingProgress.value = 100
    data.value = { user, posts, friends }
  } catch (error) {
    errors.value = [error.message]
  } finally {
    loading.value = false
    loadTime.value = Date.now() - startTime
  }
}

// 并发加载数据
const loadDataConcurrent = async () => {
  const startTime = Date.now()
  loading.value = true
  loadingProgress.value = 0
  errors.value = []

  try {
    // 先获取用户信息
    loadingProgress.value = 25
    const user = await fetchUserAPI(123)

    // 并发获取相关数据
    loadingProgress.value = 50
    const [posts, friends, settings] = await Promise.all([
      fetchUserPostsAPI(user.id),
      fetchUserFriendsAPI(user.id),
      fetchUserSettingsAPI(user.id),
    ])

    loadingProgress.value = 100
    data.value = { user, posts, friends, settings }
  } catch (error) {
    errors.value = [error.message]
  } finally {
    loading.value = false
    loadTime.value = Date.now() - startTime
  }
}

// 容错并发加载
const loadDataWithErrorHandling = async () => {
  const startTime = Date.now()
  loading.value = true
  loadingProgress.value = 0
  errors.value = []

  try {
    loadingProgress.value = 25
    const user = await fetchUserAPI(123)

    loadingProgress.value = 50
    const results = await Promise.allSettled([
      fetchUserPostsAPI(user.id),
      fetchUserFriendsAPI(user.id),
      fetchUserSettingsAPI(user.id),
      fetchUserStatsAPI(user.id),
    ])

    loadingProgress.value = 100

    // 处理结果
    const [postsResult, friendsResult, settingsResult, statsResult] = results

    data.value = {
      user,
      posts: postsResult.status === 'fulfilled' ? postsResult.value : [],
      friends: friendsResult.status === 'fulfilled' ? friendsResult.value : [],
      settings: settingsResult.status === 'fulfilled' ? settingsResult.value : null,
      stats: statsResult.status === 'fulfilled' ? statsResult.value : null,
    }

    // 收集错误
    errors.value = results
      .filter(result => result.status === 'rejected')
      .map(result => result.reason.message)
  } catch (error) {
    errors.value = [error.message]
  } finally {
    loading.value = false
    loadTime.value = Date.now() - startTime
  }
}

// 组合式函数：并发数据加载
const useConcurrentDataLoader = () => {
  const data = ref({})
  const loading = ref(false)
  const errors = ref([])

  const loadMultipleResources = async resources => {
    loading.value = true
    errors.value = []

    try {
      const results = await Promise.allSettled(
        resources.map(async resource => {
          const result = await resource.loader()
          return { key: resource.key, data: result }
        }),
      )

      // 处理成功的结果
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          data.value[result.value.key] = result.value.data
        } else {
          errors.value.push(`${resources[index].key}: ${result.reason.message}`)
        }
      })
    } finally {
      loading.value = false
    }
  }

  return { data, loading, errors, loadMultipleResources }
}
</script>
```

**并发控制的最佳实践：**

```javascript
// 1. 智能并发控制
class ConcurrencyController {
  constructor(limit = 3) {
    this.limit = limit
    this.running = 0
    this.queue = []
  }

  async execute(task) {
    return new Promise((resolve, reject) => {
      this.queue.push({ task, resolve, reject })
      this.process()
    })
  }

  async process() {
    if (this.running >= this.limit || this.queue.length === 0) {
      return
    }

    this.running++
    const { task, resolve, reject } = this.queue.shift()

    try {
      const result = await task()
      resolve(result)
    } catch (error) {
      reject(error)
    } finally {
      this.running--
      this.process()
    }
  }
}

// 2. 批量处理工具
async function batchProcess(items, processor, batchSize = 10) {
  const results = []

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    const batchResults = await Promise.all(
      batch.map(item => processor(item).catch(error => ({ error, item }))),
    )
    results.push(...batchResults)
  }

  return results
}
```

**记忆要点总结：**

- 使用Promise.all()实现真正的并发执行
- 区分串行和并发的性能差异
- Promise.allSettled()用于容错并发处理
- 合理控制并发数量避免资源过载
- 结合超时机制提高系统稳定性

# **133. [高级]** async/await在循环中的使用注意事项

## 深度分析与补充

**问题本质解读：** 这道题考察async/await在循环中的正确使用方式，面试官想了解你是否理解串行、并发执行的差异以及性能优化策略。

**知识点系统梳理：**

**async/await在循环中的核心问题：**

1. **串行 vs 并发**：不同循环方式的执行顺序
2. **性能影响**：串行执行导致的性能问题
3. **错误处理**：循环中的异常处理策略
4. **内存管理**：大量异步操作的内存控制
5. **并发限制**：避免过多并发请求

**实战应用举例：**

**通用JavaScript示例：**

```javascript
// 1. 常见错误：forEach中使用async/await
// ❌ 错误用法 - forEach不会等待async函数
async function wrongForEach(userIds) {
  const results = []

  userIds.forEach(async id => {
    const user = await fetchUser(id) // 并发执行，但无法控制
    results.push(user)
  })

  console.log(results) // 可能为空数组，因为没有等待
  return results
}

// ✅ 正确用法1 - for...of 串行执行
async function correctForOf(userIds) {
  const results = []

  for (const id of userIds) {
    const user = await fetchUser(id) // 串行执行，一个接一个
    results.push(user)
  }

  return results
}

// ✅ 正确用法2 - Promise.all 并发执行
async function correctPromiseAll(userIds) {
  const promises = userIds.map(id => fetchUser(id))
  const results = await Promise.all(promises) // 并发执行
  return results
}

// 2. 不同循环方式的对比
async function loopComparison(items) {
  console.log('=== 循环方式对比 ===')

  // for循环 - 串行执行
  console.time('for loop')
  for (let i = 0; i < items.length; i++) {
    await processItem(items[i])
  }
  console.timeEnd('for loop')

  // for...of循环 - 串行执行
  console.time('for...of')
  for (const item of items) {
    await processItem(item)
  }
  console.timeEnd('for...of')

  // map + Promise.all - 并发执行
  console.time('Promise.all')
  await Promise.all(items.map(item => processItem(item)))
  console.timeEnd('Promise.all')

  // reduce - 串行执行（高级用法）
  console.time('reduce')
  await items.reduce(async (previousPromise, item) => {
    await previousPromise
    return processItem(item)
  }, Promise.resolve())
  console.timeEnd('reduce')
}

// 3. 错误处理策略
async function errorHandlingInLoops(items) {
  // 策略1：遇到错误立即停止
  async function stopOnError() {
    try {
      for (const item of items) {
        await processItem(item) // 任一失败都会停止
      }
    } catch (error) {
      console.error('处理停止:', error)
      throw error
    }
  }

  // 策略2：收集所有错误，继续处理
  async function collectErrors() {
    const results = []
    const errors = []

    for (const item of items) {
      try {
        const result = await processItem(item)
        results.push({ item, result, status: 'success' })
      } catch (error) {
        errors.push({ item, error, status: 'failed' })
      }
    }

    return { results, errors }
  }

  // 策略3：使用Promise.allSettled
  async function useAllSettled() {
    const promises = items.map(async item => {
      try {
        const result = await processItem(item)
        return { item, result, status: 'fulfilled' }
      } catch (error) {
        return { item, error, status: 'rejected' }
      }
    })

    return await Promise.allSettled(promises)
  }

  return { stopOnError, collectErrors, useAllSettled }
}

// 4. 并发控制
async function concurrencyControl(items, limit = 3) {
  const results = []

  // 方法1：分批处理
  for (let i = 0; i < items.length; i += limit) {
    const batch = items.slice(i, i + limit)
    const batchResults = await Promise.all(
      batch.map(item => processItem(item).catch(error => ({ error, item }))),
    )
    results.push(...batchResults)
  }

  return results
}

// 5. 高级并发控制类
class AsyncIterator {
  constructor(concurrency = 3) {
    this.concurrency = concurrency
    this.running = 0
    this.queue = []
  }

  async process(items, processor) {
    const results = []
    let index = 0

    const processNext = async () => {
      if (index >= items.length) return

      const currentIndex = index++
      const item = items[currentIndex]

      this.running++

      try {
        const result = await processor(item, currentIndex)
        results[currentIndex] = { success: true, result }
      } catch (error) {
        results[currentIndex] = { success: false, error }
      } finally {
        this.running--

        if (index < items.length) {
          processNext()
        }
      }
    }

    // 启动初始并发任务
    const initialTasks = Math.min(this.concurrency, items.length)
    await Promise.all(
      Array(initialTasks)
        .fill()
        .map(() => processNext()),
    )

    // 等待所有任务完成
    while (this.running > 0) {
      await new Promise(resolve => setTimeout(resolve, 10))
    }

    return results
  }
}

// 6. 实际应用场景
async function realWorldScenarios() {
  // 场景1：批量用户数据处理
  async function batchUserProcessing(userIds) {
    const BATCH_SIZE = 5
    const results = []

    for (let i = 0; i < userIds.length; i += BATCH_SIZE) {
      const batch = userIds.slice(i, i + BATCH_SIZE)

      console.log(
        `处理批次 ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(userIds.length / BATCH_SIZE)}`,
      )

      const batchResults = await Promise.allSettled(
        batch.map(async userId => {
          const user = await fetchUser(userId)
          const profile = await fetchUserProfile(userId)
          return { user, profile }
        }),
      )

      results.push(...batchResults)

      // 批次间延迟，避免服务器压力
      if (i + BATCH_SIZE < userIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    return results
  }

  // 场景2：文件批量上传
  async function batchFileUpload(files) {
    const MAX_CONCURRENT = 3
    const results = []

    for (let i = 0; i < files.length; i += MAX_CONCURRENT) {
      const batch = files.slice(i, i + MAX_CONCURRENT)

      const batchPromises = batch.map(async (file, index) => {
        try {
          console.log(`上传文件: ${file.name}`)
          const result = await uploadFile(file)
          return { file: file.name, result, status: 'success' }
        } catch (error) {
          console.error(`上传失败: ${file.name}`, error)
          return { file: file.name, error: error.message, status: 'failed' }
        }
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)
    }

    return results
  }

  return { batchUserProcessing, batchFileUpload }
}
```

**Vue 3框架应用示例：**

```vue
<template>
  <div class="async-loop-demo">
    <div class="controls">
      <button
        @click="processSerial"
        :disabled="processing"
      >
        串行处理 (慢但稳定)
      </button>
      <button
        @click="processConcurrent"
        :disabled="processing"
      >
        并发处理 (快但可能过载)
      </button>
      <button
        @click="processControlled"
        :disabled="processing"
      >
        受控并发 (平衡)
      </button>
    </div>

    <div
      v-if="processing"
      class="progress"
    >
      <div class="progress-bar">
        <div
          class="progress-fill"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
      <p>处理进度: {{ progress }}% ({{ processedCount }}/{{ totalCount }})</p>
    </div>

    <div
      v-if="results.length > 0"
      class="results"
    >
      <h3>处理结果</h3>
      <div class="summary">
        <span class="success">成功: {{ successCount }}</span>
        <span class="failed">失败: {{ failedCount }}</span>
        <span class="time">耗时: {{ processingTime }}ms</span>
      </div>

      <div class="result-list">
        <div
          v-for="(result, index) in results.slice(0, 10)"
          :key="index"
          :class="['result-item', result.status]"
        >
          <span>项目 {{ index + 1 }}: </span>
          <span v-if="result.status === 'success'">{{ result.data }}</span>
          <span v-else>{{ result.error }}</span>
        </div>
        <div
          v-if="results.length > 10"
          class="more"
        >
          还有 {{ results.length - 10 }} 个结果...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const processing = ref(false)
const progress = ref(0)
const processedCount = ref(0)
const totalCount = ref(0)
const results = ref([])
const processingTime = ref(0)

const successCount = computed(() => results.value.filter(r => r.status === 'success').length)

const failedCount = computed(() => results.value.filter(r => r.status === 'failed').length)

// 模拟数据
const generateItems = (count = 20) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: `Item ${i + 1}`,
    shouldFail: Math.random() < 0.2, // 20% 失败率
  }))
}

// 模拟异步处理函数
const processItem = async item => {
  await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500))

  if (item.shouldFail) {
    throw new Error(`处理项目 ${item.name} 失败`)
  }

  return `${item.name} 处理完成`
}

// 串行处理
const processSerial = async () => {
  const items = generateItems()
  const startTime = Date.now()

  processing.value = true
  progress.value = 0
  processedCount.value = 0
  totalCount.value = items.length
  results.value = []

  try {
    for (let i = 0; i < items.length; i++) {
      const item = items[i]

      try {
        const data = await processItem(item)
        results.value.push({
          id: item.id,
          status: 'success',
          data,
        })
      } catch (error) {
        results.value.push({
          id: item.id,
          status: 'failed',
          error: error.message,
        })
      }

      processedCount.value = i + 1
      progress.value = Math.round(((i + 1) / items.length) * 100)
    }
  } finally {
    processing.value = false
    processingTime.value = Date.now() - startTime
  }
}

// 并发处理
const processConcurrent = async () => {
  const items = generateItems()
  const startTime = Date.now()

  processing.value = true
  progress.value = 0
  processedCount.value = 0
  totalCount.value = items.length
  results.value = []

  try {
    const promises = items.map(async item => {
      try {
        const data = await processItem(item)
        processedCount.value++
        progress.value = Math.round((processedCount.value / items.length) * 100)
        return { id: item.id, status: 'success', data }
      } catch (error) {
        processedCount.value++
        progress.value = Math.round((processedCount.value / items.length) * 100)
        return { id: item.id, status: 'failed', error: error.message }
      }
    })

    results.value = await Promise.all(promises)
  } finally {
    processing.value = false
    processingTime.value = Date.now() - startTime
  }
}

// 受控并发处理
const processControlled = async () => {
  const items = generateItems()
  const startTime = Date.now()
  const BATCH_SIZE = 3

  processing.value = true
  progress.value = 0
  processedCount.value = 0
  totalCount.value = items.length
  results.value = []

  try {
    for (let i = 0; i < items.length; i += BATCH_SIZE) {
      const batch = items.slice(i, i + BATCH_SIZE)

      const batchPromises = batch.map(async item => {
        try {
          const data = await processItem(item)
          return { id: item.id, status: 'success', data }
        } catch (error) {
          return { id: item.id, status: 'failed', error: error.message }
        }
      })

      const batchResults = await Promise.all(batchPromises)
      results.value.push(...batchResults)

      processedCount.value = Math.min(i + BATCH_SIZE, items.length)
      progress.value = Math.round((processedCount.value / items.length) * 100)
    }
  } finally {
    processing.value = false
    processingTime.value = Date.now() - startTime
  }
}
</script>

<style scoped>
.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #4caf50;
  transition: width 0.3s ease;
}

.result-item.success {
  color: #4caf50;
}

.result-item.failed {
  color: #f44336;
}
</style>
```

**最佳实践总结：**

1. **避免在forEach中使用async/await**
2. **根据需求选择串行或并发执行**
3. **使用批处理控制并发数量**
4. **合理处理循环中的错误**
5. **考虑内存和性能影响**

**记忆要点总结：**

- forEach不等待async函数，使用for...of进行串行
- Promise.all实现并发，但要注意资源限制
- 使用Promise.allSettled处理部分失败场景
- 分批处理大量数据，避免内存溢出
- 根据业务需求选择合适的并发策略

# **134. [中级]** 顶层await的概念和用法

- 可以单独使用 await
- 只能在es module中使用

## 深度分析与补充

**问题本质解读：** 这道题考察ES2022新特性顶层await，面试官想了解你是否理解模块级异步操作和现代JavaScript的发展趋势。

**知识点系统梳理：**

**顶层await的核心特性：**

1. **模块级异步**：在模块顶层直接使用await
2. **ES Module限制**：只能在ES模块中使用
3. **模块加载阻塞**：会阻塞模块的加载完成
4. **依赖传播**：影响导入该模块的其他模块
5. **错误处理**：需要合理处理顶层异步错误

**实战应用举例：**

**通用JavaScript示例：**

```javascript
// config.js - 配置模块
// ✅ 顶层await加载配置
const response = await fetch('/api/config');
const config = await response.json();

export default config;
export const { apiUrl, timeout, retries } = config;

// database.js - 数据库连接模块
// ✅ 顶层await初始化数据库连接
import { createConnection } from './db-driver.js';

const connection = await createConnection({
  host: 'localhost',
  port: 5432,
  database: 'myapp'
});

// 确保连接成功后再导出
export default connection;

// utils.js - 工具模块
// ✅ 顶层await加载外部依赖
const { default: dayjs } = await import('dayjs');
const { default: customParseFormat } = await import('dayjs/plugin/customParseFormat');

dayjs.extend(customParseFormat);

export { dayjs };

// ❌ 错误用法 - 在CommonJS中使用
// const fs = require('fs');
// const data = await fs.promises.readFile('config.json'); // SyntaxError

// ✅ 正确的CommonJS异步处理
// const fs = require('fs');
// (async () => {
//   const data = await fs.promises.readFile('config.json');
//   module.exports = JSON.parse(data);
// })();

// 实际应用场景
// 1. 环境配置加载
const isDevelopment = process.env.NODE_ENV === 'development';
const envConfig = await import(isDevelopment ? './config.dev.js' : './config.prod.js');

export const appConfig = {
  ...envConfig.default,
  loadedAt: new Date().toISOString()
};

// 2. 动态功能检测
let cryptoModule;
try {
  cryptoModule = await import('crypto');
} catch (error) {
  // 降级到Web Crypto API
  cryptoModule = { webcrypto: globalThis.crypto };
}

export const crypto = cryptoModule;

// 3. 条件性模块加载
const features = await fetch('/api/features').then(r => r.json());

const modules = {};
if (features.analytics) {
  modules.analytics = await import('./analytics.js');
}
if (features.chat) {
  modules.chat = await import('./chat.js');
}

export { modules };

// 4. 资源预加载
const criticalResources = await Promise.all([
  fetch('/api/user/current').then(r => r.json()),
  fetch('/api/app/settings').then(r => r.json()),
  import('./critical-components.js')
]);

export const [currentUser, appSettings, criticalComponents] = criticalResources;
```

**Vue 3框架应用示例：**

```javascript
// main.js - Vue应用入口
import { createApp } from 'vue'
import App from './App.vue'

// ✅ 顶层await加载应用配置
const config = await fetch('/api/app-config').then(r => r.json())

// 根据配置动态加载路由
const routerModule = config.useHashRouter
  ? await import('./router/hash.js')
  : await import('./router/history.js')

// 动态加载状态管理
const storeModule = config.useVuex
  ? await import('./store/vuex.js')
  : await import('./store/pinia.js')

// 创建应用实例
const app = createApp(App)

// 配置应用
app.use(routerModule.default)
app.use(storeModule.default)

// 全局配置
app.config.globalProperties.$config = config

// 挂载应用
app.mount('#app')

// plugins/auth.js - 认证插件
// ✅ 顶层await初始化认证状态
let authToken = localStorage.getItem('auth-token')
let currentUser = null

if (authToken) {
  try {
    const response = await fetch('/api/auth/verify', {
      headers: { Authorization: `Bearer ${authToken}` },
    })

    if (response.ok) {
      currentUser = await response.json()
    } else {
      localStorage.removeItem('auth-token')
      authToken = null
    }
  } catch (error) {
    console.error('认证验证失败:', error)
    localStorage.removeItem('auth-token')
    authToken = null
  }
}

export const authPlugin = {
  install(app) {
    app.config.globalProperties.$auth = {
      token: authToken,
      user: currentUser,
      isAuthenticated: !!currentUser,
    }
  },
}

// composables/useAsyncData.js - 组合式函数
import { ref, onMounted } from 'vue'

// ✅ 顶层await加载默认配置
const defaultConfig = await fetch('/api/default-config').then(r => r.json())

export function useAsyncData(url, options = {}) {
  const data = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const config = { ...defaultConfig, ...options }

  const fetchData = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(url, {
        timeout: config.timeout,
        retries: config.retries,
        ...config.fetchOptions,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      data.value = await response.json()
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  onMounted(fetchData)

  return { data, loading, error, refetch: fetchData }
}

// utils/feature-flags.js - 功能开关
// ✅ 顶层await加载功能开关配置
const featureFlags = await fetch('/api/feature-flags')
  .then(r => r.json())
  .catch(() => ({})) // 降级到空对象

export const isFeatureEnabled = feature => {
  return featureFlags[feature] === true
}

export const getFeatureConfig = feature => {
  return featureFlags[feature] || null
}

// 条件性功能加载
const enabledFeatures = {}

if (isFeatureEnabled('analytics')) {
  enabledFeatures.analytics = await import('./analytics.js')
}

if (isFeatureEnabled('experiments')) {
  enabledFeatures.experiments = await import('./experiments.js')
}

export { enabledFeatures }
```

**顶层await的注意事项：**

```javascript
// 1. 模块加载顺序影响
// moduleA.js
console.log('Module A: 开始加载')
await new Promise(resolve => setTimeout(resolve, 1000))
console.log('Module A: 加载完成')
export const dataA = 'A'

// moduleB.js
console.log('Module B: 开始加载')
import { dataA } from './moduleA.js' // 会等待moduleA完成
console.log('Module B: 加载完成', dataA)
export const dataB = 'B'

// 2. 错误处理
// errorModule.js
try {
  const data = await fetch('/api/critical-data').then(r => r.json())
  export const criticalData = data
} catch (error) {
  console.error('加载关键数据失败:', error)
  export const criticalData = null
}

// 3. 性能考虑
// 避免阻塞关键路径
const [essentialData, optionalData] = await Promise.allSettled([
  fetch('/api/essential').then(r => r.json()),
  fetch('/api/optional').then(r => r.json()),
])

export const essential = essentialData.status === 'fulfilled' ? essentialData.value : null

export const optional = optionalData.status === 'fulfilled' ? optionalData.value : null

// 4. 条件加载优化
const shouldLoadHeavyModule = await fetch('/api/should-load-heavy')
  .then(r => r.json())
  .then(data => data.shouldLoad)
  .catch(() => false)

export const heavyModule = shouldLoadHeavyModule ? await import('./heavy-module.js') : null
```

**浏览器兼容性和Polyfill：**

```javascript
// 检测顶层await支持
const supportsTopLevelAwait = (() => {
  try {
    new Function('await Promise.resolve()')
    return true
  } catch {
    return false
  }
})()

// 降级方案
if (!supportsTopLevelAwait) {
  // 使用IIFE包装
  ;(async () => {
    const config = await fetch('/api/config').then(r => r.json())
    // 处理配置...
  })()
}
```

**记忆要点总结：**

- 只能在ES模块中使用，不支持CommonJS
- 会阻塞模块加载，影响依赖该模块的其他模块
- 适用于模块初始化、配置加载、条件性导入
- 需要考虑错误处理和性能影响
- 现代构建工具和运行时环境支持良好

# **135. [高级]** 如何实现async/await的polyfill原理？

## 深度分析与补充

**问题本质解读：** 这道题考察async/await的底层实现原理，面试官想了解你是否理解Generator函数、Promise和自动执行器的关系。

**知识点系统梳理：**

**async/await的实现原理：**

1. **Generator函数**：提供暂停和恢复执行的能力
2. **Promise包装**：自动将返回值包装为Promise
3. **自动执行器**：自动执行Generator函数
4. **错误处理**：统一的异常处理机制
5. **状态管理**：维护异步操作的状态

**实战应用举例：**

**通用JavaScript示例：**

```javascript
// 1. 基础的async/await polyfill实现
function asyncToGenerator(generatorFunction) {
  return function (...args) {
    const generator = generatorFunction.apply(this, args)

    return new Promise((resolve, reject) => {
      function step(key, arg) {
        try {
          const info = generator[key](arg)
          const { value, done } = info

          if (done) {
            // Generator执行完成
            resolve(value)
          } else {
            // 将value包装为Promise并继续执行
            Promise.resolve(value).then(
              result => step('next', result),
              error => step('throw', error),
            )
          }
        } catch (error) {
          reject(error)
        }
      }

      step('next')
    })
  }
}

// 使用示例
function* fetchUserGenerator(userId) {
  try {
    const user = yield fetch(`/api/users/${userId}`).then(r => r.json())
    const posts = yield fetch(`/api/users/${userId}/posts`).then(r => r.json())
    return { user, posts }
  } catch (error) {
    throw new Error(`获取用户数据失败: ${error.message}`)
  }
}

// 转换为async函数
const fetchUserAsync = asyncToGenerator(fetchUserGenerator)

// 2. 更完整的实现
class AsyncFunction {
  constructor(generatorFunction) {
    this.generatorFunction = generatorFunction
  }

  call(thisArg, ...args) {
    const generator = this.generatorFunction.apply(thisArg, args)

    return new Promise((resolve, reject) => {
      const step = (method, value) => {
        let result

        try {
          result = generator[method](value)
        } catch (error) {
          return reject(error)
        }

        const { value: stepValue, done } = result

        if (done) {
          return resolve(stepValue)
        }

        // 确保返回值是Promise
        const promise = Promise.resolve(stepValue)

        promise.then(
          value => step('next', value),
          error => step('throw', error),
        )
      }

      step('next')
    })
  }
}

// 3. Babel风格的转换实现
function _asyncToGenerator(fn) {
  return function () {
    const self = this
    const args = arguments

    return new Promise((resolve, reject) => {
      const gen = fn.apply(self, args)

      function _next(value) {
        asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'next', value)
      }

      function _throw(err) {
        asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'throw', err)
      }

      _next(undefined)
    })
  }
}

function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
  try {
    const info = gen[key](arg)
    const { value, done } = info

    if (done) {
      resolve(value)
    } else {
      Promise.resolve(value).then(_next, _throw)
    }
  } catch (error) {
    reject(error)
  }
}

// 4. 支持并发的实现
function createAsyncFunction(generatorFunction) {
  const asyncFn = function (...args) {
    const generator = generatorFunction.apply(this, args)

    return new Promise((resolve, reject) => {
      const pending = new Map() // 跟踪并发操作
      let stepCount = 0

      function step(method, value) {
        const currentStep = ++stepCount

        try {
          const result = generator[method](value)
          const { value: stepValue, done } = result

          if (done) {
            resolve(stepValue)
            return
          }

          // 处理并发Promise
          if (Array.isArray(stepValue)) {
            Promise.all(stepValue).then(
              results => step('next', results),
              error => step('throw', error),
            )
          } else {
            Promise.resolve(stepValue).then(
              result => step('next', result),
              error => step('throw', error),
            )
          }
        } catch (error) {
          reject(error)
        }
      }

      step('next')
    })
  }

  // 保持原函数的属性
  Object.defineProperty(asyncFn, 'name', {
    value: generatorFunction.name,
    configurable: true,
  })

  return asyncFn
}

// 5. 实际转换示例
// 原始async函数
async function originalAsync(userId) {
  const user = await fetchUser(userId)
  const posts = await fetchPosts(user.id)
  return { user, posts }
}

// 转换后的Generator + 执行器
function* generatorVersion(userId) {
  const user = yield fetchUser(userId)
  const posts = yield fetchPosts(user.id)
  return { user, posts }
}

const polyfillAsync = _asyncToGenerator(generatorVersion)

// 6. 错误处理的完整实现
function robustAsyncToGenerator(generatorFunction) {
  return function (...args) {
    return new Promise((resolve, reject) => {
      const generator = generatorFunction.apply(this, args)
      let isSettled = false

      function settle(method, value) {
        if (isSettled) return

        try {
          const result = generator[method](value)

          if (result.done) {
            isSettled = true
            resolve(result.value)
          } else {
            Promise.resolve(result.value)
              .then(
                value => settle('next', value),
                error => settle('throw', error),
              )
              .catch(error => {
                if (!isSettled) {
                  isSettled = true
                  reject(error)
                }
              })
          }
        } catch (error) {
          if (!isSettled) {
            isSettled = true
            reject(error)
          }
        }
      }

      settle('next')
    })
  }
}

// 7. 性能优化版本
function optimizedAsyncToGenerator(generatorFunction) {
  const cache = new WeakMap()

  return function (...args) {
    // 缓存Generator实例
    let generator = cache.get(this)
    if (!generator) {
      generator = generatorFunction.apply(this, args)
      cache.set(this, generator)
    }

    return new Promise((resolve, reject) => {
      const step = (method, value) => {
        // 使用微任务优化
        queueMicrotask(() => {
          try {
            const { value: stepValue, done } = generator[method](value)

            if (done) {
              resolve(stepValue)
            } else {
              Promise.resolve(stepValue).then(
                result => step('next', result),
                error => step('throw', error),
              )
            }
          } catch (error) {
            reject(error)
          }
        })
      }

      step('next')
    })
  }
}
```

**Vue 3框架应用示例：**

```javascript
// composables/useAsyncPolyfill.js
import { ref, onMounted } from 'vue'

// 检测async/await支持
const supportsAsyncAwait = (() => {
  try {
    new Function('async () => {}')
    return true
  } catch {
    return false
  }
})()

// Polyfill实现
function createAsyncPolyfill() {
  if (supportsAsyncAwait) {
    return fn => fn // 原生支持，直接返回
  }

  return function asyncPolyfill(generatorFunction) {
    return function (...args) {
      const generator = generatorFunction.apply(this, args)

      return new Promise((resolve, reject) => {
        function step(method, value) {
          try {
            const result = generator[method](value)
            const { value: stepValue, done } = result

            if (done) {
              resolve(stepValue)
            } else {
              Promise.resolve(stepValue).then(
                result => step('next', result),
                error => step('throw', error),
              )
            }
          } catch (error) {
            reject(error)
          }
        }

        step('next')
      })
    }
  }
}

// 使用组合式函数
export function useAsyncData(url) {
  const data = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const asyncPolyfill = createAsyncPolyfill()

  // Generator版本的数据获取
  function* fetchDataGenerator() {
    loading.value = true
    error.value = null

    try {
      const response = yield fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      const result = yield response.json()
      data.value = result
      return result
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // 转换为async函数
  const fetchData = asyncPolyfill(fetchDataGenerator)

  onMounted(() => {
    fetchData()
  })

  return { data, loading, error, refetch: fetchData }
}

// utils/asyncTransform.js - 转换工具
export class AsyncTransformer {
  static transform(generatorFunction) {
    return function (...args) {
      const generator = generatorFunction.apply(this, args)

      return new Promise((resolve, reject) => {
        const step = (method, value) => {
          try {
            const result = generator[method](value)

            if (result.done) {
              resolve(result.value)
            } else {
              Promise.resolve(result.value).then(
                value => step('next', value),
                error => step('throw', error),
              )
            }
          } catch (error) {
            reject(error)
          }
        }

        step('next')
      })
    }
  }

  // 批量转换
  static transformObject(obj) {
    const transformed = {}

    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'function' && value.constructor.name === 'GeneratorFunction') {
        transformed[key] = this.transform(value)
      } else {
        transformed[key] = value
      }
    }

    return transformed
  }
}

// 使用示例
const apiMethods = {
  *fetchUser(id) {
    const response = yield fetch(`/api/users/${id}`)
    return yield response.json()
  },

  *updateUser(id, data) {
    const response = yield fetch(`/api/users/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    })
    return yield response.json()
  },
}

// 转换为async方法
const asyncApiMethods = AsyncTransformer.transformObject(apiMethods)
```

**实现原理总结：**

1. **Generator函数**：提供yield暂停点
2. **自动执行器**：递归调用next()方法
3. **Promise包装**：将yield的值包装为Promise
4. **错误传播**：通过throw()方法传播错误
5. **状态管理**：跟踪Generator的执行状态

**记忆要点总结：**

- async/await本质是Generator + Promise + 自动执行器
- yield相当于await，暂停函数执行
- 自动执行器负责递归调用Generator的next()
- 错误通过Generator的throw()方法传播
- 现代转译工具如Babel使用类似原理进行转换

### 事件循环和微任务（5道）

# **136. [中级]** JavaScript的事件循环机制是什么？

- 单线程：JS单线程一个任务接一个任务执行
- 宏任务：setTimeout setInteral UI事件 XHR的回调；每次事件循环从宏任务队列中取一个到执行完成
- 微任务：Promise.then queueMicotask的回调；事件循环中微任务优先于下一个宏任务，会将微任务队列中的全部微任务执行清空，再执行下一个宏任务
- 调用栈：存储正在执行的函数帧。同步代码直接进栈，执行完出栈
- 事件循环：不断重复：从宏任务中取出一个执行，执行完后清空微任务队列，执行渲染，执行下一个宏任务

## 深度分析与补充

**问题本质解读：** 这道题考察JavaScript异步执行机制的核心原理，面试官想了解你是否理解单线程JavaScript如何处理异步操作。

**技术错误纠正：**
- "setInteral"应该是"setInterval"
- "queueMicotask"应该是"queueMicrotask"

**知识点系统梳理：**

**事件循环的核心组件：**
1. **调用栈（Call Stack）**：执行同步代码的地方
2. **宏任务队列（Macrotask Queue）**：存储宏任务的队列
3. **微任务队列（Microtask Queue）**：存储微任务的队列
4. **Web APIs**：浏览器提供的异步API
5. **事件循环（Event Loop）**：协调各组件的执行机制

**事件循环执行流程：**
1. 执行调用栈中的同步代码
2. 调用栈清空后，执行所有微任务
3. 执行一个宏任务
4. 重复步骤2-3

**实战应用举例：**

**通用JavaScript示例：**
```javascript
// 1. 基础事件循环演示
console.log('1'); // 同步代码

setTimeout(() => {
  console.log('2'); // 宏任务
}, 0);

Promise.resolve().then(() => {
  console.log('3'); // 微任务
});

console.log('4'); // 同步代码

// 输出顺序: 1, 4, 3, 2

// 2. 复杂的执行顺序分析
function eventLoopDemo() {
  console.log('开始');

  setTimeout(() => console.log('宏任务1'), 0);

  Promise.resolve().then(() => {
    console.log('微任务1');
    return Promise.resolve();
  }).then(() => {
    console.log('微任务2');
  });

  setTimeout(() => console.log('宏任务2'), 0);

  Promise.resolve().then(() => {
    console.log('微任务3');
    setTimeout(() => console.log('宏任务3'), 0);
  });

  console.log('结束');
}

// 输出顺序: 开始, 结束, 微任务1, 微任务3, 微任务2, 宏任务1, 宏任务2, 宏任务3

// 3. 微任务队列清空机制
function microtaskClearDemo() {
  setTimeout(() => console.log('宏任务'), 0);

  Promise.resolve().then(() => {
    console.log('微任务1');
    // 在微任务中添加新的微任务
    Promise.resolve().then(() => console.log('微任务1.1'));
    queueMicrotask(() => console.log('微任务1.2'));
  });

  Promise.resolve().then(() => {
    console.log('微任务2');
    Promise.resolve().then(() => console.log('微任务2.1'));
  });
}

// 输出: 微任务1, 微任务2, 微任务1.1, 微任务1.2, 微任务2.1, 宏任务

// 4. 不同类型任务的分类
function taskClassificationDemo() {
  // 宏任务
  setTimeout(() => console.log('setTimeout'), 0);
  setInterval(() => console.log('setInterval'), 100);
  setImmediate(() => console.log('setImmediate')); // Node.js

  // 微任务
  Promise.resolve().then(() => console.log('Promise.then'));
  queueMicrotask(() => console.log('queueMicrotask'));

  // 同步代码
  console.log('同步代码');
}

// 5. 事件循环可视化工具
class EventLoopVisualizer {
  constructor() {
    this.callStack = [];
    this.macrotaskQueue = [];
    this.microtaskQueue = [];
    this.step = 0;
  }

  log(message, type = 'sync') {
    this.step++;
    console.log(`步骤${this.step}: [${type}] ${message}`);
    this.printState();
  }

  addMacrotask(task, delay = 0) {
    setTimeout(() => {
      this.macrotaskQueue.push(task);
      this.log(`添加宏任务: ${task.name}`, 'macrotask');
    }, delay);
  }

  addMicrotask(task) {
    this.microtaskQueue.push(task);
    this.log(`添加微任务: ${task.name}`, 'microtask');
  }

  executeSync(fn) {
    this.callStack.push(fn.name);
    this.log(`执行同步代码: ${fn.name}`, 'sync');
    fn();
    this.callStack.pop();
  }

  printState() {
    console.log('当前状态:', {
      callStack: this.callStack,
      macrotaskQueue: this.macrotaskQueue.map(t => t.name),
      microtaskQueue: this.microtaskQueue.map(t => t.name)
    });
  }
}

// 6. 实际应用场景
function realWorldScenarios() {
  // 场景1: 数据获取和UI更新
  function fetchAndUpdate() {
    console.log('开始获取数据');

    fetch('/api/data')
      .then(response => response.json()) // 微任务
      .then(data => {
        console.log('数据获取完成');
        // 使用setTimeout确保DOM更新在下一个事件循环
        setTimeout(() => {
          updateUI(data);
        }, 0);
      });

    console.log('请求已发送');
  }

  // 场景2: 批量操作优化
  function batchOperations() {
    const operations = [];

    // 收集操作
    for (let i = 0; i < 1000; i++) {
      operations.push(() => console.log(`操作${i}`));
    }

    // 分批执行，避免阻塞
    function executeBatch(startIndex = 0) {
      const batchSize = 10;
      const endIndex = Math.min(startIndex + batchSize, operations.length);

      for (let i = startIndex; i < endIndex; i++) {
        operations[i]();
      }

      if (endIndex < operations.length) {
        setTimeout(() => executeBatch(endIndex), 0);
      }
    }

    executeBatch();
  }

  // 场景3: 状态同步
  function stateSync() {
    let state = { count: 0 };
    const listeners = [];

    function setState(newState) {
      state = { ...state, ...newState };

      // 使用微任务确保状态更新的同步性
      Promise.resolve().then(() => {
        listeners.forEach(listener => listener(state));
      });
    }

    function subscribe(listener) {
      listeners.push(listener);
    }

    return { setState, subscribe };
  }
}
```

**Vue 3框架应用示例：**
```vue
<template>
  <div class="event-loop-demo">
    <div class="controls">
      <button @click="demonstrateEventLoop">演示事件循环</button>
      <button @click="clearLogs">清空日志</button>
    </div>

    <div class="visualization">
      <div class="section">
        <h3>调用栈</h3>
        <div class="stack">
          <div
            v-for="(item, index) in callStack"
            :key="index"
            class="stack-item"
          >
            {{ item }}
          </div>
        </div>
      </div>

      <div class="section">
        <h3>微任务队列</h3>
        <div class="queue">
          <div
            v-for="(task, index) in microtaskQueue"
            :key="index"
            class="task-item microtask"
          >
            {{ task }}
          </div>
        </div>
      </div>

      <div class="section">
        <h3>宏任务队列</h3>
        <div class="queue">
          <div
            v-for="(task, index) in macrotaskQueue"
            :key="index"
            class="task-item macrotask"
          >
            {{ task }}
          </div>
        </div>
      </div>
    </div>

    <div class="logs">
      <h3>执行日志</h3>
      <div class="log-container">
        <div
          v-for="(log, index) in logs"
          :key="index"
          :class="['log-item', log.type]"
        >
          <span class="step">{{ log.step }}</span>
          <span class="type">[{{ log.type }}]</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'

const callStack = ref([])
const microtaskQueue = ref([])
const macrotaskQueue = ref([])
const logs = ref([])
let stepCounter = 0

const addLog = (message, type = 'sync') => {
  stepCounter++
  logs.value.push({
    step: stepCounter,
    type,
    message,
    timestamp: Date.now()
  })
}

const demonstrateEventLoop = async () => {
  stepCounter = 0
  logs.value = []
  callStack.value = []
  microtaskQueue.value = []
  macrotaskQueue.value = []

  addLog('开始演示事件循环', 'sync')

  // 模拟调用栈
  callStack.value.push('demonstrateEventLoop')

  // 添加宏任务
  setTimeout(() => {
    addLog('执行宏任务1: setTimeout', 'macrotask')
    macrotaskQueue.value.shift()
  }, 0)
  macrotaskQueue.value.push('setTimeout-1')
  addLog('添加宏任务: setTimeout', 'schedule')

  // 添加微任务
  Promise.resolve().then(() => {
    addLog('执行微任务1: Promise.then', 'microtask')
    microtaskQueue.value.shift()

    // 在微任务中添加新的微任务
    Promise.resolve().then(() => {
      addLog('执行微任务1.1: 嵌套Promise.then', 'microtask')
      microtaskQueue.value.shift()
    })
    microtaskQueue.value.push('nested-promise')
  })
  microtaskQueue.value.push('promise-1')
  addLog('添加微任务: Promise.then', 'schedule')

  // 添加更多任务
  queueMicrotask(() => {
    addLog('执行微任务2: queueMicrotask', 'microtask')
    microtaskQueue.value.shift()
  })
  microtaskQueue.value.push('queueMicrotask')
  addLog('添加微任务: queueMicrotask', 'schedule')

  setTimeout(() => {
    addLog('执行宏任务2: setTimeout', 'macrotask')
    macrotaskQueue.value.shift()
  }, 0)
  macrotaskQueue.value.push('setTimeout-2')
  addLog('添加宏任务: setTimeout', 'schedule')

  addLog('同步代码执行完成', 'sync')
  callStack.value.pop()

  // 使用nextTick观察Vue的更新时机
  await nextTick()
  addLog('Vue nextTick 执行', 'vue')
}

const clearLogs = () => {
  logs.value = []
  callStack.value = []
  microtaskQueue.value = []
  macrotaskQueue.value = []
  stepCounter = 0
}

// 组合式函数：事件循环调试工具
const useEventLoopDebugger = () => {
  const taskQueue = ref([])

  const scheduleTask = (task, type = 'macrotask', delay = 0) => {
    const taskInfo = {
      id: Date.now() + Math.random(),
      name: task.name || 'anonymous',
      type,
      scheduled: Date.now(),
      delay
    }

    taskQueue.value.push(taskInfo)

    if (type === 'macrotask') {
      setTimeout(() => {
        task()
        removeTask(taskInfo.id)
      }, delay)
    } else if (type === 'microtask') {
      Promise.resolve().then(() => {
        task()
        removeTask(taskInfo.id)
      })
    }
  }

  const removeTask = (taskId) => {
    const index = taskQueue.value.findIndex(task => task.id === taskId)
    if (index > -1) {
      taskQueue.value.splice(index, 1)
    }
  }

  return { taskQueue, scheduleTask }
}
</script>

<style scoped>
.event-loop-demo {
  padding: 20px;
}

.visualization {
  display: flex;
  gap: 20px;
  margin: 20px 0;
}

.section {
  flex: 1;
  border: 1px solid #ddd;
  padding: 10px;
  border-radius: 5px;
}

.stack, .queue {
  min-height: 100px;
  border: 1px dashed #ccc;
  padding: 10px;
}

.stack-item, .task-item {
  background: #f0f0f0;
  padding: 5px;
  margin: 2px 0;
  border-radius: 3px;
}

.task-item.microtask {
  background: #e3f2fd;
}

.task-item.macrotask {
  background: #fff3e0;
}

.logs {
  margin-top: 20px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 10px;
}

.log-item {
  padding: 2px 0;
  font-family: monospace;
}

.log-item.sync { color: #333; }
.log-item.microtask { color: #1976d2; }
.log-item.macrotask { color: #f57c00; }
.log-item.schedule { color: #666; }
.log-item.vue { color: #4caf50; }

.step {
  display: inline-block;
  width: 30px;
  font-weight: bold;
}

.type {
  display: inline-block;
  width: 80px;
  font-weight: bold;
}
</style>
```

**记忆要点总结：**
- 调用栈执行同步代码，清空后处理异步任务
- 微任务优先级高于宏任务，会清空整个微任务队列
- 每次事件循环：同步代码 → 微任务队列 → 一个宏任务
- 常见宏任务：setTimeout、setInterval、I/O、UI事件
- 常见微任务：Promise.then、queueMicrotask、MutationObserver

# **137. [中级]** 宏任务和微任务的区别

- 宏任务：用于分发事件与任务
- 微任务：用于在当前任务（宏任务）末尾立即执行剩余逻辑，微任务会在渲染之前全部跑完

# **138. [高级]** Promise.resolve()在事件循环中的执行时机

- Promise.resolve()
  - 如果是单个参数之间运行 属于同步代码 直接执行；
  - 如果是后续的then方法运行 属于异步代码 是微任务 在某次微任务队列中按照顺序执行

# **139. [中级]** setTimeout(0)和Promise.resolve()的执行顺序

- setTimeout是宏任务
- Promise.resolve() 直接执行是同步任务 先于setTimeout； Promise.resolve().then() 是微任务，在某次宏任务执行完成后末尾一次性执行完全部微任务队列内的微任务

# **140. [高级]** 如何理解事件循环的执行栈、任务队列和微任务队列？

- 宏任务：setTimeout setInteral UI事件 XHR的回调；每次事件循环从宏任务队列中取一个到执行完成
- 微任务：Promise.then queueMicotask的回调；事件循环中微任务优先于下一个宏任务，会将微任务队列中的全部微任务执行清空，再执行下一个宏任务
- 调用栈：存储正在执行的函数帧。同步代码直接进栈，执行完出栈
- 事件循环：不断重复：从宏任务中取出一个执行，执行完后清空微任务队列，执行渲染，执行下一个宏任务

---

## 现代JavaScript特性

### 模块化（5道）

# **141. [中级]** ES6模块与CommonJS模块的区别

- Es6 module 是采用 import 和 export 的方式；模块内的作用域不提升到外部；可以动态导入；可以导出任何值
- commonJs 是采用 require的方式

# **142. [中级]** import和export的各种用法

- import Xxx from './xxx.js' 导入类或者函数； 需要在文件中导出 export default Xxx
- import {a,b,c} from './xxx.js' 导入多个方法； 在文件中可以定义多个导出内容 export const a = 1； export function b(){}
- import \* as from './xxx.js'
- import './style.js'
- export {Xxx} from './xxx.js'

# **143. [中级]** 动态import()的用法和应用场景

- cosnt MyComponent = await import('./xxx.js')

# **144. [高级]** 模块的循环依赖问题如何解决？

-

# **145. [中级]** Tree-shaking的原理是什么？

- tree-shaking 将没有引用但是没有实际使用的组件剔除（或者注释），以减轻包体积和提高代码编译速度
- 原理：标记清除

### 新API和特性（5道）

# **146. [中级]** Proxy对象的作用和基本用法

```javascript
const obj = {
  a:1,
  b:[2,3,4],
  c:'hello',
  d:true,
  e:{x:Symbol('x'),y:new Date()},
  f:(x)=>`${x}`,
}

const proxy = new Proxy(obj,{
  get:(target,propKey,receiver){
  return Reflcet.get(target,propKey,receiver)
	}
  set:(target,propKey,receiver){
    return Reflcet.set(target,propKey,receiver)
  }
})
```

# **147. [高级]** Reflect对象提供了哪些方法？

- 提供了和Proxy相同的方法
- 13个静态方法
  - Reflect.apply(target, thisArg, args)
  - Reflect.construct(target, args)
  - Reflect.get(target, name, receiver)
  - Reflect.set(target, name, value, receiver)
  - Reflect.defineProperty(target, name, desc)
  - Reflect.deleteProperty(target, name)
  - Reflect.has(target, name)
  - Reflect.ownKeys(target)
  - Reflect.isExtensible(target)
  - Reflect.preventExtensions(target)
  - Reflect.getOwnPropertyDescriptor(target, name)
  - Reflect.getPrototypeOf(target)
  - Reflect.setPrototypeOf(target, prototype)

# **148. [中级]** 可选链操作符(?.)的用法

- 代替 && 前段确认链

```javascript
const obj = {
  a:{
    b:{
      c:{
        d:1234,
        e:[5,6,7],
        f:x => x+2
      }
    }
  }
}

const e2 = obj?.a?.b?.c?.e?[2]
const foo = obj?.a?.b?.c?.f
obj?.a?.b?.c?.f?.(3)
```

# **149. [中级]** 空值合并操作符(??)的作用

- 仅判断前置是否为 undefined 和 null

# **150. [中级]** BigInt数据类型的特点和用法

- 用于表示number不能表示的数字 进行计算操作
- 数字后面跟一个n

---
