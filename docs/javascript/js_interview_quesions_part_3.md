
## 异步编程

### Promise基础（12道）

# **116. [初级]** 什么是Promise？它解决了什么问题？

promise es6新增的用于处理异步请求的新方式。通过链式调用解决了之前的回调地狱问题。promise 状态一旦发生变化就不会再改变





# **117. [初级]** Promise有哪几种状态？

- pending 等待
- fulfilled 执行成功的原因
- rejected 执行拒绝的理由



# **118. [中级]** 如何创建一个Promise？

- new Promise
- Promise()



# **119. [中级]** Promise.then()方法的用法

- pormise.then() 参数是一个执行器方法，返回一个promise.thenable。
- 通常为：(resolve,reject) =>{ 执行成功的resolve 或者失败的reject }
- 当执行器函数只有一个参数时，默认为：resolve



# **120. [中级]** Promise的链式调用如何工作？

- 通过返回一个promise的thenable 在下一个then中接收到上一个then的结果，继续执行下一步的操作



# **121. [中级]** Promise.catch()和try-catch的区别

- promise.catch 捕获Promise中异步操作的异常和then中异常
- 包含在promise外部的try catch 可以捕获promise执行中的异常



# **122. [中级]** 如何处理多个Promise？

- Promise.all([]) 可以通过数组的方式将多个异步操作同时并行请求，只有全部成功才成功，有一个失败都失败。返回全部请求执行结果
- Promise.race([]) 可以通过数组的方式将多个异步操作同时竞争请求，只有全部失败才都失败，返回最快完成请求的那个结果
- Promise.allSettled([]) 可以通过数组的方式将多个异步操作同时并行请求,返回各个请求的结果（结果中包含结果状态和值）
- Promise.any([]) 可以通过数组的方式将多个异步操作同时竞争请求, 只要有一个请求成功，返回最先成功的那个，只有全部失败的时候才失败



# **123. [中级]** Promise.all()和Promise.allSettled()的区别

- Promise.all([]) 可以通过数组的方式将多个异步操作同时并行请求，只有全部成功才成功，有一个失败都失败。返回全部请求执行结果
- Promise.allSettled([]) 可以通过数组的方式将多个异步操作同时并行请求,返回各个请求的结果（结果中包含结果状态和值）
  1. 整体完成的时机不同：
     1. all 只要其中有一个失败就都失败
     2. allSettled 不论其中的失败和成功 全部执行
  2. 返回的结果的结构不同：
     1. all 根据数组顺序返回对应顺序的结果
     2. allSettled 根据数组顺序返回对应顺序的对象内容 包括了完成状态 stuas 和请求结果 value



# **124. [高级]** Promise.race()的使用场景

- 用于包装请求函数中的超时操作。可以在Promise.all 中包含一个异步请求的同时可以再加入一个Promise.race 包含的超时操作



# **125. [中级]** 如何取消一个Promise？

无法取消promise



# **126. [高级]** 如何实现一个简单的Promise？

```javascript
const FULFILLED = 'fulfilled'
const REJECTED = 'rejected'
const PENDING = 'pending'

function MyPromise(exectue){
  this.state = PENDING
  this.reason = null
  this.value = null
  
  this.onFulfilledCallbacks = []
  this.onRejectedCallbacks = []
  
  const that = this
  function resolve(value){
    if(this.state === PENDING){
      this.value = value
      this.state = FULFILLED
      that.onFulfilledCallbacks.forEach(fn=>fn(that.value))
    }
  }
  
  function reject(reason){
    if(this.state === PENDING){
      this.reason = resaon
      this.state = REJECTED
      that.onRejectedCallbacks.forEach(fn=>fn(that.resaon))
    }
  }
  
  try{
    exectue(resolve,reject)
  }catch(err){
    reject(err)
  }
}

MyPromise.prototype.then = function(onFullfilled,onRejected){
  if(typeof onFullfilled !== 'function'){
    onFullfilled = function(value){
      return value
    }
  }
  
  if(typeof onRejected !== 'function'){
    onRejected = function(reason){
      throw reason
    }
  }
  
  const that = this
  
  if(this.state === FULFILLED){
    const promise2 = new MyPromiose((resolve,reject)=>{
      setTimeout(()=>{
        try{
         if(typeof onFullfilled !== 'function'){
           resolve(that.value)
         } else{
           const x = onFullfilled(that.value)
           resoveMyPromise(promise2,x,resolve,reject)
         }
        }catch(err){
          reject(err)
        }
      },0)
    })
    return promise2
  }
  
  if(this.state === REJECTED){
    const promise2 = new MyPromiose((resolve,reject)=>{
      setTimeout(()=>{
        try{
         if(typeof onRejected !== 'function'){
           reject(that.reason)
         } else{
           const x = onRejected(that.reason)
           resoveMyPromise(promise2,x,resolve,reject)
         }
        }catch(err){
          reject(err)
        }
      },0)
    })
    return promise2
  }
  
  if(this.state === PENDING){
    const promise2 = new MyPromise((resolve,reject)=>{
      this.onFulfilledCallbacks.push(()=>{
        setTimeout(()=>{
        try{
         if(typeof onFullfilled !== 'function'){
           resolve(that.value)
         } else{
           const x = onFullfilled(that.value)
           resoveMyPromise(promise2,x,resolve,reject)
         }
        }catch(err){
          reject(err)
        }
      },0)
      })
      this.onRejectedCallbacks.push(()=>{
        setTimeout(()=>{
        try{
         if(typeof onRejected !== 'function'){
           reject(that.reason)
         } else{
           const x = onRejected(that.reason)
           resoveMyPromise(promise2,x,resolve,reject)
         }
        }catch(err){
          reject(err)
        }
      },0)
      })
    })
    return promise2
  }
}

MyPromise.prototype.catch = function(onRejected){
  return this.then(null,onRejected)
}

MyPromise.prototype.finally = function(onFinally){
  return this.then(onFinally,onFinally)
}

MyPromise.resolve = function(value){
  if(value instanceof MyPromise){
    return value
  }
  
  return new MyPromise((resolve,reject)=>{
    resolve(value)
  })
}

MyPromise.reject = function(reason){
  return new MyPromise((resolve,reject)=>{
    reject(reason)
  })
}

MyPromise.prototype.all = function(promises=[]){
  return new MyPromise((resolve,reject)=>{
    const result = []
    let count = 0
    
    if(promises.length === 0){
      resolve(result)
    }
    
    for(const promise of promises){
      MyPromise.resolve(promise).then((value)=>{
        count++
        result.push(value)
        if(count === promises.length){
          resolve(reslut)
        }
      },(reason)=>{
        reject(reason)
      })
    }
  })
}

function resolveMyPromise(promise, x, resolve, reject) {
  if (x === promise) {
    return reject(new TypeError("promise and x is same value"))
  }

  if (x instanceof MyPromise) {
    x.then((y) => {
      resolveMyPromise(promise, y, resolve, reject)
    }, reject)
  } else if (typeof x === "object" || typeof x === "function") {
    if (x === null) {
      return resolve(x)
    }

    try {
      var then = x.then
    } catch (err) {
      reject(err)
    }

    if (typeof then === "function") {
      let called = false

      try {
        then.call(
          x,
          (y) => {
            if (called) return
            called = true
            resolveMyPromise(promise, y, resolve, reject)
          },
          (r) => {
            if (called) return
            called = true
            reject(r)
          }
        )
      } catch (e) {
        if (called) return
        reject(e)
      }
    } else {
      resolve(x)
    }
  } else {
    resolve(x)
  }
}
```



# **127. [中级]** Promise中的错误传播机制

.catch() 



### async/await（8道）

# **128. [初级]** async/await的基本用法

```javascript
async function(){
  await fetch('/get/github/user=pan').then((response)=>response.json())
}
```



# **129. [中级]** async函数返回什么？

- 返回一个promise



# **130. [中级]** 如何在async函数中处理错误？

- 在外部包一个try-catch 捕获异常



# **131. [中级]** async/await相比Promise的优势

- async await 本质上是 generator和promise 结合的语法糖，使用更加简洁，心智模型更加简单，以同步（类似）的方式来执行异步操作，可维护性更强



# **132. [中级]** 如何并发执行多个async操作？

- Promise.all 
- Promise.race



# **133. [高级]** async/await在循环中的使用注意事项

- 

# **134. [中级]** 顶层await的概念和用法

- 可以单独使用 await
- 只能在es module中使用

# **135. [高级]** 如何实现async/await的polyfill原理？

- 

### 事件循环和微任务（5道）

# **136. [中级]** JavaScript的事件循环机制是什么？

- 单线程：JS单线程一个任务接一个任务执行
- 宏任务：setTimeout setInteral UI事件 XHR的回调；每次事件循环从宏任务队列中取一个到执行完成
- 微任务：Promise.then queueMicotask的回调；事件循环中微任务优先于下一个宏任务，会将微任务队列中的全部微任务执行清空，再执行下一个宏任务
- 调用栈：存储正在执行的函数帧。同步代码直接进栈，执行完出栈
- 事件循环：不断重复：从宏任务中取出一个执行，执行完后清空微任务队列，执行渲染，执行下一个宏任务



# **137. [中级]** 宏任务和微任务的区别

- 宏任务：用于分发事件与任务
- 微任务：用于在当前任务（宏任务）末尾立即执行剩余逻辑，微任务会在渲染之前全部跑完



# **138. [高级]** Promise.resolve()在事件循环中的执行时机

- Promise.resolve() 
  - 如果是单个参数之间运行 属于同步代码 直接执行；
  - 如果是后续的then方法运行 属于异步代码 是微任务 在某次微任务队列中按照顺序执行



# **139. [中级]** setTimeout(0)和Promise.resolve()的执行顺序

- setTimeout是宏任务 
- Promise.resolve() 直接执行是同步任务 先于setTimeout； Promise.resolve().then() 是微任务，在某次宏任务执行完成后末尾一次性执行完全部微任务队列内的微任务



# **140. [高级]** 如何理解事件循环的执行栈、任务队列和微任务队列？

- 宏任务：setTimeout setInteral UI事件 XHR的回调；每次事件循环从宏任务队列中取一个到执行完成
- 微任务：Promise.then queueMicotask的回调；事件循环中微任务优先于下一个宏任务，会将微任务队列中的全部微任务执行清空，再执行下一个宏任务
- 调用栈：存储正在执行的函数帧。同步代码直接进栈，执行完出栈
- 事件循环：不断重复：从宏任务中取出一个执行，执行完后清空微任务队列，执行渲染，执行下一个宏任务

---

## 现代JavaScript特性

### 模块化（5道）

# **141. [中级]** ES6模块与CommonJS模块的区别

- Es6 module 是采用 import 和 export 的方式；模块内的作用域不提升到外部；可以动态导入；可以导出任何值
- commonJs 是采用 require的方式



# **142. [中级]** import和export的各种用法

- import Xxx from './xxx.js'  导入类或者函数； 需要在文件中导出 export default Xxx
- import {a,b,c} from './xxx.js' 导入多个方法； 在文件中可以定义多个导出内容 export const a = 1； export function b(){}
- import * as from './xxx.js'
- import './style.js'
- export {Xxx} from './xxx.js'

# **143. [中级]** 动态import()的用法和应用场景

- cosnt MyComponent = await import('./xxx.js')



# **144. [高级]** 模块的循环依赖问题如何解决？

- 

# **145. [中级]** Tree-shaking的原理是什么？

- tree-shaking 将没有引用但是没有实际使用的组件剔除（或者注释），以减轻包体积和提高代码编译速度
- 原理：标记清除

### 新API和特性（5道）

# **146. [中级]** Proxy对象的作用和基本用法

```javascript
const obj = {
  a:1,
  b:[2,3,4],
  c:'hello',
  d:true,
  e:{x:Symbol('x'),y:new Date()},
  f:(x)=>`${x}`,
}

const proxy = new Proxy(obj,{
  get:(target,propKey,receiver){
  return Reflcet.get(target,propKey,receiver)
	}
  set:(target,propKey,receiver){
    return Reflcet.set(target,propKey,receiver)
  }
})
```



# **147. [高级]** Reflect对象提供了哪些方法？

- 提供了和Proxy相同的方法
- 13个静态方法
  - Reflect.apply(target, thisArg, args)
  - Reflect.construct(target, args)
  - Reflect.get(target, name, receiver)
  - Reflect.set(target, name, value, receiver)
  - Reflect.defineProperty(target, name, desc)
  - Reflect.deleteProperty(target, name)
  - Reflect.has(target, name)
  - Reflect.ownKeys(target)
  - Reflect.isExtensible(target)
  - Reflect.preventExtensions(target)
  - Reflect.getOwnPropertyDescriptor(target, name)
  - Reflect.getPrototypeOf(target)
  - Reflect.setPrototypeOf(target, prototype)

# **148. [中级]** 可选链操作符(?.)的用法

- 代替 && 前段确认链

```javascript
const obj = {
  a:{
    b:{
      c:{
        d:1234,
        e:[5,6,7],
        f:x => x+2
      }
    }
  }
}

const e2 = obj?.a?.b?.c?.e?[2]
const foo = obj?.a?.b?.c?.f
obj?.a?.b?.c?.f?.(3)
```



# **149. [中级]** 空值合并操作符(??)的作用

- 仅判断前置是否为 undefined 和 null

# **150. [中级]** BigInt数据类型的特点和用法

- 用于表示number不能表示的数字 进行计算操作
- 数字后面跟一个n

---